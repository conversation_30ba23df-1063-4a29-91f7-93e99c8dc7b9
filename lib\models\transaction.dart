class Transaction {
  final String id;
  final String userId;
  final String talabatAccountNumber;
  final String senderWalletNumber; // ✅ جديد
  final double amount;
  final double commission;
  final double totalAmount;
  final String walletType;
  final DateTime date;
  final String status;


  Transaction({
    required this.id,
    required this.userId,
    required this.talabatAccountNumber,
    required this.senderWalletNumber, // ✅ جديد
    required this.amount,
    required this.commission,
    required this.totalAmount,
    required this.walletType,
    required this.date,
    required this.status,

  });

  factory Transaction.fromMap(Map<String, dynamic> map, String docId) {
    return Transaction(
      id: docId,
      userId: map['userId'] ?? '',
      talabatAccountNumber: map['talabatAccountNumber'] ?? '',
      senderWalletNumber: map['senderWalletNumber'] ?? '', // ✅ جديد
      amount: (map['amount'] ?? 0).toDouble(),
      commission: (map['commission'] ?? 0).toDouble(),
      totalAmount: (map['totalAmount'] ?? 0).toDouble(),
      walletType: map['walletType'] ?? '',
      date: map['date']?.toDate() ?? DateTime.now(),
      status: map['status'] ?? 'جاري',

    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'talabatAccountNumber': talabatAccountNumber,
      'senderWalletNumber': senderWalletNumber, // ✅ جديد
      'amount': amount,
      'commission': commission,
      'totalAmount': totalAmount,
      'walletType': walletType,
      'date': date,
      'status': status,

    };
  }

  Transaction copyWith({
    String? id,
    String? userId,
    String? talabatAccountNumber,
    String? senderWalletNumber, // ✅ جديد
    double? amount,
    double? commission,
    double? totalAmount,
    String? walletType,
    DateTime? date,
    String? status,

  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      talabatAccountNumber: talabatAccountNumber ?? this.talabatAccountNumber,
      senderWalletNumber: senderWalletNumber ?? this.senderWalletNumber, // ✅ جديد
      amount: amount ?? this.amount,
      commission: commission ?? this.commission,
      totalAmount: totalAmount ?? this.totalAmount,
      walletType: walletType ?? this.walletType,
      date: date ?? this.date,
      status: status ?? this.status,

    );
  }
}

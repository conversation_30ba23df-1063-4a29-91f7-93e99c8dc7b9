import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  // ضع هنا القيم من Supabase Dashboard
  static const String supabaseUrl = 'https://vymeazhbvktmzillvnxc.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ5bWVhemhidmt0bXppbGx2bnhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNzk1NjcsImV4cCI6MjA2Njg1NTU2N30.l6zkXbh_h_EfkIQ2FZyYDod6qafA24tfCbsNHcavmGE';
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
    );
  }
  
  // للوصول السريع لـ Supabase client
  static SupabaseClient get client => Supabase.instance.client;
}

// مثال على استخدام Supabase
class SupabaseService {
  static final _client = SupabaseConfig.client;
  
  // إضافة مستخدم جديد
  static Future<bool> addUser({
    required String name,
    required String phoneNumber,
    required String secretCode,
  }) async {
    try {
      await _client.from('users').insert({
        'name': name,
        'phone_number': phoneNumber,
        'secret_code': secretCode,
        'created_at': DateTime.now().toIso8601String(),
        'favorite_accounts': [],
      });
      return true;
    } catch (e) {
      print('خطأ في إضافة المستخدم: $e');
      return false;
    }
  }
  
  // البحث عن مستخدم بالهاتف والرقم السري
  static Future<Map<String, dynamic>?> loginUser({
    required String phoneNumber,
    required String secretCode,
  }) async {
    try {
      final response = await _client
          .from('users')
          .select()
          .eq('phone_number', phoneNumber)
          .eq('secret_code', secretCode)
          .single();
      
      return response;
    } catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      return null;
    }
  }
  
  // الحصول على جميع المستخدمين (للتشخيص)
  static Future<List<Map<String, dynamic>>> getAllUsers() async {
    try {
      final response = await _client.from('users').select();
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('خطأ في جلب المستخدمين: $e');
      return [];
    }
  }
  
  // تحديث آخر تسجيل دخول
  static Future<bool> updateLastLogin(String phoneNumber) async {
    try {
      await _client
          .from('users')
          .update({'last_login': DateTime.now().toIso8601String()})
          .eq('phone_number', phoneNumber);
      return true;
    } catch (e) {
      print('خطأ في تحديث آخر تسجيل دخول: $e');
      return false;
    }
  }
}

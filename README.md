# تطبيق وردلي (Wardly)

تطبيق موبايل مبني بـ Flutter لتسهيل عملية توريد الأموال من مندوبي شركة "طلبات" في مصر إلى حساباتهم داخل تطبيق طلبات.

## نظرة عامة

يعمل تطبيق "وردلي" كوسيط رقمي يمكّن المندوب من إرسال الأموال من أي محفظة رقمية مثل:
- Vodafone Cash
- Etisalat Cash
- Orange Money
- We Pay
- Instapay

ويقوم التطبيق بتحويل المبلغ إلى حساب المندوب في "طلبات"، مقابل عمولة ثابتة قدرها 0.5% من المبلغ.

## المميزات

- **تسجيل الدخول**: تسجيل دخول آمن برقم الهاتف والتحقق عبر رمز OTP
- **توريد سريع**: واجهة سهلة الاستخدام لتوريد الأموال
- **سجل المعاملات**: عرض تاريخ كامل للمعاملات السابقة
- **حساب العمولة تلقائيًا**: حساب العمولة (0.5%) وإظهار المبلغ الإجمالي
- **دعم فني**: تواصل مباشر مع فريق الدعم الفني عبر واتساب
- **واجهة عربية**: تطبيق كامل باللغة العربية
- **تصميم بسيط**: واجهة مستخدم بسيطة وحديثة

## المنصات المدعومة

🤖 **Android:** الإصدار 5.0 أو أحدث (API 21+)
🌐 **الويب:** Chrome, Firefox, Safari, Edge

## متطلبات التشغيل

- Flutter SDK: 2.19.0 أو أحدث
- Dart SDK: 2.19.0 أو أحدث
- Android Studio (للتطوير على Android)
- متصفح ويب حديث

## التثبيت والإعداد

1. تأكد من تثبيت Flutter SDK على جهازك
2. استنسخ المشروع:
   ```
   git clone https://github.com/yourusername/wardly_app.git
   ```
3. انتقل إلى مجلد المشروع:
   ```
   cd wardly_app
   ```
4. قم بتثبيت التبعيات:
   ```
   flutter pub get
   ```
5. قم بتشغيل التطبيق:
   ```bash
   # للأندرويد
   flutter run -d android

   # للويب
   flutter run -d chrome
   ```

## البناء للإنتاج

### Android:
```bash
# بناء APK
flutter build apk --release

# بناء App Bundle (للنشر في Google Play)
flutter build appbundle --release
```

### الويب:
```bash
# بناء للويب
flutter build web --release
```

## هيكل المشروع

```
lib/
├── main.dart                  # نقطة الدخول الرئيسية للتطبيق
├── models/                    # نماذج البيانات
│   ├── transaction.dart       # نموذج المعاملة
│   └── user.dart              # نموذج المستخدم
├── providers/                 # مزودي الحالة (State Providers)
│   ├── auth_provider.dart     # مزود المصادقة
│   └── transaction_provider.dart # مزود المعاملات
├── screens/                   # شاشات التطبيق
│   ├── auth/
│   │   ├── login_screen.dart  # شاشة تسجيل الدخول
│   │   └── otp_verification_screen.dart # شاشة التحقق من OTP
│   ├── home_screen.dart       # الشاشة الرئيسية
│   ├── new_transaction_screen.dart # شاشة توريد جديد
│   ├── splash_screen.dart     # شاشة البداية
│   └── transaction_history_screen.dart # شاشة سجل المعاملات
├── utils/                     # أدوات مساعدة
│   └── app_theme.dart         # موضوع التطبيق والألوان
└── widgets/                   # واجهات مخصصة
    ├── custom_button.dart     # زر مخصص
    ├── otp_input.dart         # حقل إدخال OTP
    └── transaction_list_item.dart # عنصر قائمة المعاملات
```

## التطوير المستقبلي

- تحسين نظام قاعدة البيانات (Supabase)
- إضافة نظام إشعارات لتنبيه المستخدم بحالة المعاملات
- إضافة خيارات محافظ إلكترونية إضافية
- تحسين واجهة المستخدم وتجربة المستخدم
- إضافة ميزة المفضلة لحسابات طلبات المتكررة

## الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).

## الاتصال والدعم

للاستفسارات أو الدعم، يرجى التواصل عبر البريد الإلكتروني: <EMAIL>
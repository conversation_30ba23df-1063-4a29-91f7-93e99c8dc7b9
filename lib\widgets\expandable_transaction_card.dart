import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/transaction.dart';
import '../utils/app_theme.dart';

class ExpandableTransactionCard extends StatefulWidget {
  final Transaction transaction;

  const ExpandableTransactionCard({
    Key? key,
    required this.transaction,
  }) : super(key: key);

  @override
  State<ExpandableTransactionCard> createState() => _ExpandableTransactionCardState();
}

class _ExpandableTransactionCardState extends State<ExpandableTransactionCard> {
  bool _isExpanded = false;

  // الحصول على لون حالة المعاملة
  Color _getStatusColor() {
    switch (widget.transaction.status) {
      case 'تم':
        return AppTheme.successColor;
      case 'جاري':
        return AppTheme.pendingColor;
      case 'مرفوض':
        return AppTheme.errorColor;
      default:
        return Colors.grey;
    }
  }

  // الحصول على أيقونة المحفظة
  IconData _getWalletIcon() {
    switch (widget.transaction.walletType) {
      case 'Vodafone Cash':
        return Icons.phone_android;
      case 'Etisalat Cash':
        return Icons.phone_android;
      case 'Orange Money':
        return Icons.account_balance_wallet;
      case 'We Pay':
        return Icons.account_balance_wallet;
      case 'Instapay':
        return Icons.credit_card;
      case 'CIB Smart Wallet':
        return Icons.account_balance_wallet;
      default:
        return Icons.account_balance_wallet;
    }
  }

  @override
  Widget build(BuildContext context) {
    // تنسيق التاريخ
    final dateFormat = DateFormat('dd/MM/yyyy - hh:mm a');
    final formattedDate = dateFormat.format(widget.transaction.date);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          // الجزء الأساسي من البطاقة (دائماً مرئي)
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // أيقونة المحفظة
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    _getWalletIcon(),
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                // معلومات المعاملة الأساسية
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.transaction.walletType,
                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      Text(
                        formattedDate,
                        style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                // المبلغ وحالة المعاملة
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${widget.transaction.amount.toStringAsFixed(2)} جنيه',
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    const SizedBox(height: 4),
                    // حالة المعاملة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor().withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        widget.transaction.status,
                        style: TextStyle(
                          color: _getStatusColor(),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 8),
                // زر التفاصيل
                IconButton(
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: AppTheme.primaryColor,
                  ),
                  tooltip: _isExpanded ? 'إخفاء التفاصيل' : 'عرض التفاصيل',
                ),
              ],
            ),
          ),
          // الجزء القابل للتوسيع (التفاصيل)
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isExpanded ? null : 0,
            child: _isExpanded
                ? Container(
                    width: double.infinity,
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    child: Column(
                      children: [
                        const Divider(height: 1),
                        const SizedBox(height: 16),
                        // تفاصيل المعاملة
                        _buildDetailRow('رقم حساب طلبات:', widget.transaction.talabatAccountNumber),
                        const SizedBox(height: 8),
                        _buildDetailRow('رقم المحفظة المُرسلة:', widget.transaction.senderWalletNumber),
                        const SizedBox(height: 8),
                        _buildDetailRow('العمولة:', '${widget.transaction.commission.toStringAsFixed(2)} جنيه'),
                        const SizedBox(height: 8),
                        _buildDetailRow('إجمالي المبلغ:', '${widget.transaction.totalAmount.toStringAsFixed(2)} جنيه'),


                      ],
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }



  // بناء صف التفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}

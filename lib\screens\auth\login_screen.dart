import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import 'package:wardly_app/providers/supabase_auth_provider.dart';
import 'package:wardly_app/screens/auth/secret_code_screen.dart';
import 'package:wardly_app/screens/auth/existing_user_login_screen.dart';
import 'package:wardly_app/utils/app_theme.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  bool _isNameValid = false;
  bool _isPhoneValid = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  // التحقق من صحة اسم العميل
  void _validateName(String value) {
    setState(() {
      // التحقق من أن الاسم يحتوي على حروف فقط وأكثر من حرفين
      final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$'); // حروف عربية وإنجليزية ومسافات
      _isNameValid = value.trim().length >= 2 && nameRegex.hasMatch(value.trim());
    });
  }

  // التحقق من صحة رقم الهاتف
  void _validatePhone(String value) {
    setState(() {
      // التحقق من أن رقم الهاتف يتكون من 11 رقم ويبدأ بـ 01 (بعد +2) ويحتوي على أرقام فقط
      final phoneRegex = RegExp(r'^[0-9]+$');
      _isPhoneValid = value.length == 11 &&
                     value.startsWith('01') &&
                     phoneRegex.hasMatch(value);
    });
  }

  // التحقق من صحة جميع البيانات
  bool get _isFormValid => _isNameValid && _isPhoneValid;





  // تفعيل الحساب عبر WhatsApp مع إضافة تلقائية
  Future<void> _requestAccountActivation(BuildContext context) async {
    // الحصول على البيانات المدخلة
    final customerName = _nameController.text.trim();
    final phoneNumber = _phoneController.text.trim();
    final fullPhoneNumber = '+2$phoneNumber';

    // إنشاء رقم سري عشوائي
    final secretCode = _generateSecretCode();

    try {
      // فحص الاتصال بالإنترنت أولاً
      final hasInternet = await _checkInternetConnection();
      if (!hasInternet) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('⚠️ لا يوجد اتصال بالإنترنت. تحقق من الاتصال وحاول مرة أخرى.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }
        return;
      }

      // عرض رسالة تحميل
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('⏳ جاري إنشاء الحساب...'),
            backgroundColor: Colors.blue,
            duration: Duration(seconds: 2),
          ),
        );
      }

      // إضافة المستخدم تلقائياً في Supabase
      final addResult = await _addUserDirectlyToSupabase(customerName, fullPhoneNumber, secretCode);

      if (addResult['success']) {
        // إنشاء رسالة WhatsApp بالتنسيق المطلوب
        final message = '''مرحباً، أريد تفعيل حساب جديد في تطبيق وردلي:

  الاسم: $customerName
  رقم الهاتف: $fullPhoneNumber

يرجى تفعيل الحساب وإرسال الرقم السري للدخول.

شكراً لكم.''';

        // رقم WhatsApp للمدير
        const adminWhatsApp = '+201286462589';

        // إنشاء رابط WhatsApp
        final whatsappUrl = 'https://wa.me/$adminWhatsApp?text=${Uri.encodeComponent(message)}';

        // فتح WhatsApp
        await _launchWhatsApp(whatsappUrl);

        // عرض رسالة نجاح
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال طلب تفعيل الحساب! سيتم إرسال الرقم السري لك قريباً.'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 4),
            ),
          );

          // الانتقال إلى شاشة إدخال الرقم السري
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => SecretCodeScreen(
                customerName: customerName,
                phoneNumber: fullPhoneNumber,
              ),
            ),
          );
        }
      } else {
        // إذا فشل في الإضافة (مثل رقم مكرر)
        if (addResult['error'].contains('duplicate')) {
          // المستخدم موجود مسبقاً - توجيه لتسجيل الدخول
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('هذا الحساب موجود مسبقاً. يرجى استخدام "تسجيل الدخول" بدلاً من تفعيل الحساب.'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 4),
              ),
            );
          }
        } else {
          // خطأ آخر
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في إنشاء الحساب: ${addResult['error']}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        String errorMessage = 'خطأ في العملية';

        if (e.toString().contains('SocketException') || e.toString().contains('Failed host lookup')) {
          errorMessage = 'خطأ في الاتصال بالإنترنت. تحقق من:\n• اتصال الإنترنت\n• إعدادات DNS\n• جرب بيانات الهاتف بدلاً من WiFi';
        } else if (e.toString().contains('TimeoutException')) {
          errorMessage = 'انتهت مهلة الاتصال. جرب مرة أخرى.';
        } else {
          errorMessage = 'خطأ غير متوقع: ${e.toString().substring(0, 100)}...';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 6),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _requestAccountActivation(context),
            ),
          ),
        );
      }
    }
  }

  // إنشاء رقم سري عشوائي
  String _generateSecretCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString(); // رقم من 6 خانات
  }



  // إرسال إشعار للبوت
  Future<void> _notifyTelegramBot(String name, String phoneNumber, String secretCode) async {
    try {
      // إرسال لبوت Wardly Admin Bot (تفعيل الحسابات)
      const botToken = '**********:AAGyG4Ji9hCOk8Ljs4W_GwtA4BMb1qXmnhw';
      const adminChatId = '*********';

      final message = '''
🔔 [تفعيل حساب] طلب تفعيل جديد!

👤 الاسم: $name
📱 رقم الهاتف: $phoneNumber
🔑 الرقم السري: $secretCode
📅 الوقت: ${DateTime.now().toString().substring(0, 16)}

💬 تم إنشاء الحساب تلقائياً من التطبيق
🎯 اضغط الزر أدناه لإرسال الرقم السري للعميل
''';

      final url = Uri.parse('https://api.telegram.org/bot$botToken/sendMessage');

      // إنشاء رسالة الرقم السري لـ WhatsApp
      final whatsappMessage = 'مرحباً، رقم التفعيل الخاص بك في تطبيق وردلي هو: $secretCode';
      final whatsappUrl = 'https://wa.me/${phoneNumber.replaceAll('+', '')}?text=${Uri.encodeComponent(whatsappMessage)}';

      // إنشاء لوحة مفاتيح مع زر WhatsApp
      final keyboard = {
        'inline_keyboard': [
          [
            {
              'text': '📱 إرسال الرقم السري',
              'url': whatsappUrl
            }
          ]
        ]
      };

      await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'chat_id': adminChatId,
          'text': message,
          'reply_markup': keyboard,
        }),
      );

      // تم إرسال الإشعار بنجاح أو فشل بصمت
    } catch (e) {
      // فشل في إرسال الإشعار - لا يؤثر على العملية الأساسية
    }
  }

  // فحص الاتصال بالإنترنت
  Future<bool> _checkInternetConnection() async {
    try {
      final response = await http.get(
        Uri.parse('https://www.google.com'),
      ).timeout(const Duration(seconds: 5));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // إضافة مستخدم مباشرة إلى Supabase
  Future<Map<String, dynamic>> _addUserDirectlyToSupabase(String name, String phoneNumber, String secretCode) async {
    try {
      const supabaseUrl = 'https://vymeazhbvktmzillvnxc.supabase.co';
      const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ5bWVhemhidmt0bXppbGx2bnhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNzk1NjcsImV4cCI6MjA2Njg1NTU2N30.l6zkXbh_h_EfkIQ2FZyYDod6qafA24tfCbsNHcavmGE';

      final url = Uri.parse('$supabaseUrl/rest/v1/users');

      final headers = {
        'apikey': supabaseKey,
        'Authorization': 'Bearer $supabaseKey',
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      };

      final body = json.encode({
        'name': name,
        'phone_number': phoneNumber,
        'secret_code': secretCode,
        'created_at': DateTime.now().toIso8601String(),
        'favorite_accounts': []
      });

      final response = await http.post(url, headers: headers, body: body)
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 201) {
        // إرسال إشعار للبوت
        _notifyTelegramBot(name, phoneNumber, secretCode);

        return {'success': true, 'data': json.decode(response.body)};
      } else {
        return {'success': false, 'error': 'HTTP ${response.statusCode}: ${response.body}'};
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // فتح WhatsApp
  Future<void> _launchWhatsApp(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح WhatsApp';
      }
    } catch (e) {
      // للويب: فتح في نافذة جديدة
      if (kIsWeb) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.platformDefault);
      } else {
        rethrow;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<SupabaseAuthProvider>(context);

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                const SizedBox(height: 40),
                // لوجو التطبيق
                Center(
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Center(
                      child: Text(
                        'وردلي',
                        style: TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 48),
                // عنوان الشاشة
                const Text(
                  'تسجيل الدخول',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                // وصف الشاشة
                const Text(
                  'أدخل اسمك ورقم هاتفك لتفعيل حسابك',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                // حقل إدخال اسم العميل
                TextFormField(
                  controller: _nameController,
                  keyboardType: TextInputType.name,
                  textDirection: TextDirection.rtl,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[\u0600-\u06FFa-zA-Z\s]')), // حروف عربية وإنجليزية ومسافات فقط
                  ],
                  decoration: const InputDecoration(
                    labelText: 'اسم العميل',
                    hintText: 'أدخل اسمك',
                    prefixIcon: Icon(Icons.person),
                  ),
                  onChanged: _validateName,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال اسم العميل';
                    }
                    if (value.trim().length < 2) {
                      return 'يجب أن يكون الاسم أكثر من حرفين';
                    }
                    final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s]+$');
                    if (!nameRegex.hasMatch(value.trim())) {
                      return 'يجب أن يحتوي الاسم على حروف فقط';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                // حقل إدخال رقم الهاتف
                TextFormField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  textDirection: TextDirection.ltr,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly, // أرقام فقط
                    LengthLimitingTextInputFormatter(11), // حد أقصى 11 رقم بعد +2
                  ],
                  decoration: const InputDecoration(
                    labelText: 'رقم الهاتف',
                    hintText: 'أدخل رقم هاتفك',
                    prefixIcon: Icon(Icons.phone),
                    prefixText: '+2 ',
                    prefixStyle: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    counterText: '', // إخفاء عداد الأحرف
                  ),
                  onChanged: _validatePhone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رقم الهاتف';
                    }
                    if (value.length != 11) {
                      return 'رقم الهاتف يجب أن يكون 11 رقم بالضبط';
                    }
                    if (!value.startsWith('01')) {
                      return 'رقم الهاتف يجب أن يبدأ بـ 01';
                    }
                    if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                      return 'يرجى إدخال أرقام فقط';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 32),

                // زر تفعيل الحساب - ظاهر دائماً
                ElevatedButton.icon(
                  onPressed: (_isFormValid && !authProvider.isLoading) ? () => _requestAccountActivation(context) : null,
                  icon: authProvider.isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(
                          Icons.sms,
                          size: 24,
                          color: Colors.white,
                        ),
                  label: Text(
                    authProvider.isLoading ? 'جاري التفعيل...' : 'تفعيل الحساب',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isFormValid ? AppTheme.primaryColor : Colors.grey[400],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: _isFormValid ? 3 : 1,
                    minimumSize: const Size(double.infinity, 56),
                  ),
                ),

                const SizedBox(height: 16),

                // زر تسجيل الدخول
                OutlinedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ExistingUserLoginScreen(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.login, size: 24),
                  label: const Text(
                    'تسجيل الدخول',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                    side: const BorderSide(width: 2),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    minimumSize: const Size(double.infinity, 56),
                  ),
                ),

                const SizedBox(height: 40),
                // نص الشروط والأحكام
                const Text(
                  'بالضغط على متابعة، أنت توافق على شروط الاستخدام وسياسة الخصوصية',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
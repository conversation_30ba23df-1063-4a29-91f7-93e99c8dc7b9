import 'dart:async';
import 'dart:io';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdManager {
  // تبديل بين إعلانات الاختبار والحقيقية
  static const bool _useTestAds = true; // غير إلى false للإعلانات الحقيقية

  static String get rewardedAdUnitId {
    if (Platform.isAndroid) {
      if (_useTestAds) {
        // معرف الاختبار - يعمل دائماً
        return 'ca-app-pub-3940256099942544/5224354917';
      } else {
        // معرف الإعلان الحقيقي من AdMob
        return 'ca-app-pub-9880594970188723/1686981520';
      }
    } else if (Platform.isIOS) {
      if (_useTestAds) {
        // معرف الاختبار - يعمل دائماً
        return 'ca-app-pub-3940256099942544/1712485313';
      } else {
        // معرف الإعلان الحقيقي من AdMob
        return 'ca-app-pub-9880594970188723/1686981520';
      }
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  static String get bannerAdUnitId {
    if (Platform.isAndroid) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/6300978111';
    } else if (Platform.isIOS) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/2934735716';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  static String get interstitialAdUnitId {
    if (Platform.isAndroid) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/1033173712';
    } else if (Platform.isIOS) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/4411468910';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  // تهيئة الإعلانات
  static Future<void> initialize() async {
    await MobileAds.instance.initialize();
  }

  // إنشاء إعلان مكافأة
  static Future<RewardedAd?> createRewardedAd() async {
    print('🔄 بدء تحميل الإعلان...');
    print('📱 Ad Unit ID: $rewardedAdUnitId');
    print('🧪 استخدام إعلانات اختبار: $_useTestAds');

    try {
      final completer = Completer<RewardedAd?>();

      RewardedAd.load(
        adUnitId: rewardedAdUnitId,
        request: const AdRequest(),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (RewardedAd ad) {
            print('✅ تم تحميل الإعلان بنجاح');
            completer.complete(ad);
          },
          onAdFailedToLoad: (LoadAdError error) {
            print('❌ فشل في تحميل الإعلان:');
            print('   - كود الخطأ: ${error.code}');
            print('   - رسالة الخطأ: ${error.message}');
            print('   - المجال: ${error.domain}');
            print('   - السبب المحتمل: ${error.responseInfo}');
            completer.complete(null);
          },
        ),
      );

      // انتظار لمدة 30 ثانية كحد أقصى
      return await completer.future.timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('⏰ انتهت مهلة تحميل الإعلان (30 ثانية)');
          return null;
        },
      );
    } catch (e) {
      print('❌ خطأ في تحميل الإعلان: $e');
      return null;
    }
  }

  // عرض إعلان مكافأة
  static void showRewardedAd(
    RewardedAd ad, {
    required Function() onAdWatched,
    required Function() onAdFailed,
  }) {
    ad.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (RewardedAd ad) {
        print('📺 بدء عرض الإعلان');
      },
      onAdDismissedFullScreenContent: (RewardedAd ad) {
        print('❌ تم إغلاق الإعلان');
        ad.dispose();
        onAdFailed();
      },
      onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
        print('❌ فشل في عرض الإعلان: $error');
        ad.dispose();
        onAdFailed();
      },
    );

    ad.setImmersiveMode(true);
    
    ad.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
      print('🎉 تم مشاهدة الإعلان بنجاح!');
      print('💰 المكافأة المستلمة: ${reward.amount} ${reward.type}');
      onAdWatched();
    });
  }
}

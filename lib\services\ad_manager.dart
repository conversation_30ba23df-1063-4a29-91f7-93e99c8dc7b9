import 'dart:io';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdManager {
  static String get rewardedAdUnitId {
    if (Platform.isAndroid) {
      // معرف الإعلان الحقيقي من AdMob
      return 'ca-app-pub-9880594970188723/1686981520';
    } else if (Platform.isIOS) {
      // معرف الإعلان الحقيقي من AdMob (نفس المعرف للاختبار)
      return 'ca-app-pub-9880594970188723/1686981520';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  static String get bannerAdUnitId {
    if (Platform.isAndroid) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/6300978111';
    } else if (Platform.isIOS) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/2934735716';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  static String get interstitialAdUnitId {
    if (Platform.isAndroid) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/1033173712';
    } else if (Platform.isIOS) {
      // معرف الإعلان للاختبار - استبدله بمعرف حقيقي لاحق<|im_start|>ة
      return 'ca-app-pub-3940256099942544/4411468910';
    } else {
      throw UnsupportedError('Unsupported platform');
    }
  }

  // تهيئة الإعلانات
  static Future<void> initialize() async {
    await MobileAds.instance.initialize();
  }

  // إنشاء إعلان مكافأة
  static Future<RewardedAd?> createRewardedAd() async {
    RewardedAd? rewardedAd;
    
    await RewardedAd.load(
      adUnitId: rewardedAdUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          print('✅ تم تحميل الإعلان بنجاح');
          rewardedAd = ad;
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('❌ فشل في تحميل الإعلان: $error');
          rewardedAd = null;
        },
      ),
    );
    
    return rewardedAd;
  }

  // عرض إعلان مكافأة
  static void showRewardedAd(
    RewardedAd ad, {
    required Function() onAdWatched,
    required Function() onAdFailed,
  }) {
    ad.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (RewardedAd ad) {
        print('📺 بدء عرض الإعلان');
      },
      onAdDismissedFullScreenContent: (RewardedAd ad) {
        print('❌ تم إغلاق الإعلان');
        ad.dispose();
        onAdFailed();
      },
      onAdFailedToShowFullScreenContent: (RewardedAd ad, AdError error) {
        print('❌ فشل في عرض الإعلان: $error');
        ad.dispose();
        onAdFailed();
      },
    );

    ad.setImmersiveMode(true);
    
    ad.show(onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
      print('🎉 تم مشاهدة الإعلان بنجاح!');
      print('💰 المكافأة المستلمة: ${reward.amount} ${reward.type}');
      onAdWatched();
    });
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wardly_app/models/user.dart';
import 'package:wardly_app/supabase_config.dart';

class SupabaseAuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  bool _isAuthenticated = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _isAuthenticated;
  String? get error => _error;

  // Constructor
  SupabaseAuthProvider() {
    // تأخير تحميل البيانات لتجنب مشاكل التهيئة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserFromLocalStorage();
    });
  }

  // تحديد حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تحديد رسالة الخطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // تحميل بيانات المستخدم من التخزين المحلي
  Future<void> _loadUserFromLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userIdString = prefs.getString('user_id');
      final userName = prefs.getString('user_name');
      final userPhone = prefs.getString('user_phone');
      final adViewsRemaining = prefs.getInt('ad_views_remaining') ?? 20;

      if (userIdString != null && userName != null && userPhone != null) {
        final userId = int.tryParse(userIdString) ?? 0;
        _currentUser = UserModel(
          id: userId,
          name: userName,
          phoneNumber: userPhone,
          createdAt: DateTime.now(),
          lastLogin: DateTime.now(),
          favoriteAccounts: [],
          adViewsRemaining: adViewsRemaining,
        );
        _isAuthenticated = true;
        notifyListeners();
      }
    } catch (e) {
      print('❌ خطأ في تحميل بيانات المستخدم: $e');
      // في حالة فشل تحميل البيانات، نتجاهل الخطأ
      _isAuthenticated = false;
      _currentUser = null;
      notifyListeners();
    }
  }

  // حفظ بيانات المستخدم في التخزين المحلي
  Future<void> _saveUserToLocalStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_currentUser != null) {
        await prefs.setString('user_id', _currentUser!.id.toString());
        await prefs.setString('user_name', _currentUser!.name ?? '');
        await prefs.setString('user_phone', _currentUser!.phoneNumber);
      }
    } catch (e) {
      // خطأ في حفظ البيانات المحلية
    }
  }

  // تسجيل الدخول بالرقم السري
  Future<bool> loginWithSecretCode({
    required String phoneNumber,
    required String secretCode,
    required String customerName,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      print('🔄 بدء تسجيل الدخول بـ Supabase...');
      print('📱 رقم الهاتف: $phoneNumber');
      print('🔑 الرقم السري: $secretCode');

      // البحث عن المستخدم في Supabase
      final response = await SupabaseService.loginUser(
        phoneNumber: phoneNumber,
        secretCode: secretCode,
      );

      if (response == null) {
        _setError('رقم الهاتف أو الرقم السري غير صحيح');
        _setLoading(false);
        return false;
      }

      print('✅ تم العثور على المستخدم: ${response['name']}');

      // إنشاء مستخدم محلي
      _currentUser = UserModel(
        id: response['id'] is int ? response['id'] : int.tryParse(response['id'].toString()) ?? 0,
        name: response['name'],
        phoneNumber: response['phone_number'],
        createdAt: DateTime.parse(response['created_at']),
        lastLogin: DateTime.now(),
        favoriteAccounts: List<String>.from(response['favorite_accounts'] ?? []),
      );

      // تحديث آخر تسجيل دخول
      await SupabaseService.updateLastLogin(phoneNumber);

      // حفظ في التخزين المحلي
      await _saveUserToLocalStorage();

      _isAuthenticated = true;
      _setLoading(false);
      return true;
    } catch (e) {
      print('❌ خطأ في تسجيل الدخول: $e');
      _setError('حدث خطأ في تسجيل الدخول. حاول مرة أخرى');
      _setLoading(false);
      return false;
    }
  }

  // اختبار اتصال Supabase
  Future<bool> testSupabaseConnection() async {
    try {
      print('🔍 اختبار اتصال Supabase...');
      
      final users = await SupabaseService.getAllUsers();
      print('✅ Supabase متصل - عدد المستخدمين: ${users.length}');
      
      return true;
    } catch (e) {
      print('❌ خطأ في اتصال Supabase: $e');
      return false;
    }
  }

  // عرض جميع المستخدمين (للتشخيص)
  Future<void> debugShowAllUsers() async {
    try {
      print('📋 عرض جميع المستخدمين في Supabase:');
      final users = await SupabaseService.getAllUsers();
      
      if (users.isEmpty) {
        print('❌ لا يوجد مستخدمين في جدول "users"');
        return;
      }
      
      print('📊 عدد المستخدمين: ${users.length}');
      
      for (int i = 0; i < users.length; i++) {
        final user = users[i];
        print('👤 مستخدم ${i + 1}:');
        print('   - ID: ${user['id']}');
        print('   - الاسم: ${user['name']}');
        print('   - رقم الهاتف: ${user['phone_number']}');
        print('   - الرقم السري: ${user['secret_code']}');
        print('   - تاريخ الإنشاء: ${user['created_at']}');
        print('   ---');
      }
    } catch (e) {
      print('❌ خطأ في عرض المستخدمين: $e');
    }
  }

  // تسجيل الدخول بالهاتف والرمز السري
  Future<bool> signInWithPhoneAndCode(String phoneNumber, String secretCode) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // محاولة تسجيل الدخول

      // البحث عن المستخدم في قاعدة البيانات
      final response = await SupabaseConfig.client
          .from('users')
          .select()
          .eq('phone_number', phoneNumber)
          .eq('secret_code', secretCode)
          .maybeSingle();

      if (response != null) {
        // تم العثور على المستخدم
        _currentUser = UserModel.fromMap(response, response['id']);
        _isAuthenticated = true;

        // حفظ بيانات المستخدم محلياً
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_name', _currentUser!.name ?? '');
        await prefs.setString('user_phone', _currentUser!.phoneNumber);
        await prefs.setString('user_id', _currentUser!.id.toString());
        await prefs.setInt('ad_views_remaining', _currentUser!.adViewsRemaining);
        await prefs.setBool('is_authenticated', true);

        // تحديث آخر تسجيل دخول
        await SupabaseConfig.client
            .from('users')
            .update({'last_login': DateTime.now().toIso8601String()})
            .eq('id', _currentUser!.id);

        notifyListeners();
        return true;
      } else {
        // لم يتم العثور على المستخدم
        _error = 'رقم الهاتف أو الرمز السري غير صحيح';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'خطأ في تسجيل الدخول: $e';
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // تسجيل الخروج
  Future<void> signOut() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      _currentUser = null;
      _isAuthenticated = false;
      _error = null;

      notifyListeners();
    } catch (e) {
      _error = 'خطأ في تسجيل الخروج: $e';
      notifyListeners();
    }
  }

  // تحديث عدد مشاهدات الإعلان المتبقية
  Future<bool> updateAdViewsRemaining(int newCount) async {
    try {
      if (_currentUser == null) return false;

      // تحديث في قاعدة البيانات
      await SupabaseConfig.client
          .from('users')
          .update({'ad_views_remaining': newCount})
          .eq('id', _currentUser!.id);

      // تحديث المستخدم المحلي
      _currentUser = _currentUser!.copyWith(adViewsRemaining: newCount);

      // حفظ في التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('ad_views_remaining', newCount);

      notifyListeners();
      return true;
    } catch (e) {
      _error = 'خطأ في تحديث عدد مشاهدات الإعلان: $e';
      notifyListeners();
      return false;
    }
  }

  // تقليل عدد مشاهدات الإعلان بواحد
  Future<bool> decrementAdViews() async {
    if (_currentUser == null || _currentUser!.adViewsRemaining <= 0) {
      return false;
    }

    final newCount = _currentUser!.adViewsRemaining - 1;
    return await updateAdViewsRemaining(newCount);
  }

  // إعادة تعيين الخطأ
  void resetError() {
    _error = null;
    notifyListeners();
  }
}

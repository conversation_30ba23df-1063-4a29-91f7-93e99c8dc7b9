import 'dart:async';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class CountdownTimer extends StatefulWidget {
  final VoidCallback? onTimerFinished;

  const CountdownTimer({
    Key? key,
    this.onTimerFinished,
  }) : super(key: key);

  @override
  State<CountdownTimer> createState() => _CountdownTimerState();
}

class _CountdownTimerState extends State<CountdownTimer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Timer _timer;
  int _remainingSeconds = 300; // 5 دقائق = 300 ثانية
  bool _isFinished = false;

  @override
  void initState() {
    super.initState();
    
    // إعداد animation controller
    _animationController = AnimationController(
      duration: const Duration(seconds: 300), // 5 دقائق
      vsync: this,
    );

    // بدء العد التنازلي
    _startTimer();
    
    // بدء الانيميشن
    _animationController.forward();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0) {
          _remainingSeconds--;
        } else {
          _isFinished = true;
          _timer.cancel();
          _animationController.stop();
          if (widget.onTimerFinished != null) {
            widget.onTimerFinished!();
          }
        }
      });
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    _animationController.dispose();
    super.dispose();
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _contactSupport() async {
    const message = '''
مرحباً، أحتاج للحصول على الرقم السري مرة أخرى.

يرجى إرسال الرقم السري للدخول إلى التطبيق.

شكراً لكم.
''';
    
    const adminWhatsApp = '+201286462589';
    final whatsappUrl = 'https://wa.me/$adminWhatsApp?text=${Uri.encodeComponent(message)}';
    
    try {
      final Uri uri = Uri.parse(whatsappUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح WhatsApp';
      }
    } catch (e) {
      // للويب: فتح في نافذة جديدة
      await launchUrl(Uri.parse(whatsappUrl), mode: LaunchMode.platformDefault);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120,
      height: 120,
      child: _isFinished
          ? _buildContactButton()
          : _buildTimer(),
    );
  }

  Widget _buildTimer() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // الدائرة الخارجية (الخلفية)
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.grey[200],
          ),
        ),
        
        // الدائرة المتحركة (التقدم)
        SizedBox(
          width: 120,
          height: 120,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return CircularProgressIndicator(
                value: 1.0 - _animationController.value,
                strokeWidth: 8,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _remainingSeconds > 60 
                      ? Colors.blue 
                      : _remainingSeconds > 30 
                          ? Colors.orange 
                          : Colors.red,
                ),
              );
            },
          ),
        ),
        
        // النص في المنتصف
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.timer,
              size: 24,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(_remainingSeconds),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            Text(
              'متبقي',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContactButton() {
    return GestureDetector(
      onTap: _contactSupport,
      child: Container(
        width: 120,
        height: 120,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.green[600],
          boxShadow: [
            BoxShadow(
              color: Colors.green.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat,
              size: 32,
              color: Colors.white,
            ),
            SizedBox(height: 4),
            Text(
              'تواصل معنا',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

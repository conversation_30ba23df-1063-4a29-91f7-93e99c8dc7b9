import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:wardly_app/providers/supabase_auth_provider.dart';
import 'package:wardly_app/providers/transaction_provider.dart';
import 'package:wardly_app/screens/splash_screen.dart';
import 'package:wardly_app/utils/app_theme.dart';
import 'package:wardly_app/supabase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // معالجة الأخطاء العامة
  FlutterError.onError = (FlutterErrorDetails details) {
    print('❌ Flutter Error: ${details.exception}');
    print('Stack trace: ${details.stack}');
  };

  try {
    // تهيئة Supabase
    await SupabaseConfig.initialize();
    print('✅ Supabase initialized successfully');
  } catch (e) {
    print('❌ Supabase initialization failed: $e');
    // لا نوقف التطبيق، نتركه يعمل بدون Supabase
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (_) {
            try {
              return SupabaseAuthProvider();
            } catch (e) {
              print('❌ Error creating SupabaseAuthProvider: $e');
              return SupabaseAuthProvider(); // إرجاع instance فارغ
            }
          },
        ),
        ChangeNotifierProvider(
          create: (_) {
            try {
              return TransactionProvider();
            } catch (e) {
              print('❌ Error creating TransactionProvider: $e');
              return TransactionProvider(); // إرجاع instance فارغ
            }
          },
        ),
      ],
      child: MaterialApp(
        title: 'وردلي',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.lightTheme,
        locale: const Locale('ar', 'EG'),
        supportedLocales: const [Locale('ar', 'EG')],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        home: const SplashScreen(),
        // معالجة أخطاء البناء
        builder: (context, widget) {
          ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
            return Scaffold(
              backgroundColor: Colors.white,
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 60,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'حدث خطأ في التطبيق',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'يرجى إعادة تشغيل التطبيق',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        // إعادة تشغيل التطبيق
                        Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(builder: (_) => const SplashScreen()),
                          (route) => false,
                        );
                      },
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            );
          };
          return widget!;
        },
      ),
    );
  }
}


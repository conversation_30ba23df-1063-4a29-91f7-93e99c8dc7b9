import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:wardly_app/providers/supabase_auth_provider.dart';
import 'package:wardly_app/screens/home_screen.dart';
import 'package:wardly_app/utils/app_theme.dart';

class ExistingUserLoginScreen extends StatefulWidget {
  const ExistingUserLoginScreen({super.key});

  @override
  State<ExistingUserLoginScreen> createState() => _ExistingUserLoginScreenState();
}

class _ExistingUserLoginScreenState extends State<ExistingUserLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _secretCodeController = TextEditingController();

  bool _isFormValid = false;

  @override
  void initState() {
    super.initState();
    _phoneController.addListener(_validateForm);
    _secretCodeController.addListener(_validateForm);
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _secretCodeController.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _isFormValid = _phoneController.text.trim().length == 11 &&
                    _secretCodeController.text.trim().length >= 4;
    });
  }





  // تسجيل الدخول
  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;
    if (!mounted) return;

    try {
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      final phoneNumber = '+2${_phoneController.text.trim()}';
      final secretCode = _secretCodeController.text.trim();

      print('🔄 محاولة تسجيل الدخول...');
      print('📱 رقم الهاتف: $phoneNumber');
      print('🔑 الرقم السري: $secretCode');

      final success = await authProvider.signInWithPhoneAndCode(phoneNumber, secretCode);

      if (!mounted) return;

      if (success) {
        print('✅ تم تسجيل الدخول بنجاح');

        // تسجيل دخول ناجح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ تم تسجيل الدخول بنجاح!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // تأخير قصير قبل الانتقال
        await Future.delayed(const Duration(milliseconds: 500));

        if (!mounted) return;

        // الانتقال إلى الشاشة الرئيسية
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const HomeScreen()),
          (route) => false,
        );
      } else {
        print('❌ فشل تسجيل الدخول');

        // فشل تسجيل الدخول
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.error ?? '❌ رقم الهاتف أو الرمز السري غير صحيح'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الدخول: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ خطأ في تسجيل الدخول: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        title: const Text(
          'تسجيل الدخول',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: Consumer<SupabaseAuthProvider>(
        builder: (context, authProvider, child) {
          return SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                    const SizedBox(height: 40),

                    // أيقونة تسجيل الدخول
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.login,
                        size: 50,
                        color: AppTheme.primaryColor,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // عنوان الشاشة
                    const Text(
                      'أهلاً بعودتك!',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    // وصف الشاشة
                    const Text(
                      'أدخل رقم هاتفك والرمز السري للدخول',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 32),

                    // حقل رقم الهاتف
                    TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(11),
                      ],
                      decoration: InputDecoration(
                        labelText: 'رقم الهاتف',
                        hintText: 'أدخل رقم هاتفك',
                        prefixIcon: const Icon(Icons.phone),
                        prefixText: '+2 ',
                        prefixStyle: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال رقم الهاتف';
                        }
                        if (value.trim().length != 11) {
                          return 'رقم الهاتف يجب أن يكون 11 رقم';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 16),

                    // حقل الرمز السري
                    TextFormField(
                      controller: _secretCodeController,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(10),
                      ],
                      decoration: InputDecoration(
                        labelText: 'الرمز السري',
                        hintText: 'أدخل الرمز السري',
                        prefixIcon: const Icon(Icons.lock),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'يرجى إدخال الرمز السري';
                        }
                        if (value.trim().length < 4) {
                          return 'الرمز السري يجب أن يكون 4 أرقام على الأقل';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 32),

                    // زر الدخول
                    ElevatedButton.icon(
                      onPressed: (_isFormValid && !authProvider.isLoading) ? _login : null,
                      icon: authProvider.isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Icon(
                              Icons.login,
                              size: 24,
                              color: Colors.white,
                            ),
                      label: Text(
                        authProvider.isLoading ? 'جاري تسجيل الدخول...' : 'دخول',
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isFormValid ? AppTheme.primaryColor : Colors.grey[400],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: _isFormValid ? 3 : 1,
                        minimumSize: const Size(double.infinity, 56),
                      ),
                    ),

                    const SizedBox(height: 40),

                    // نص المساعدة
                    const Text(
                      'إذا نسيت الرمز السري، تواصل مع الدعم الفني',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

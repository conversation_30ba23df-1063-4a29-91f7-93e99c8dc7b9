class UserModel {
  final int id;
  final String phoneNumber;
  final String? name;
  final String? secretCode;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final List<String> favoriteAccounts; // حسابات طلبات المفضلة
  final int adViewsRemaining; // عدد مرات مشاهدة الإعلان المتبقية

  UserModel({
    required this.id,
    required this.phoneNumber,
    this.name,
    this.secretCode,
    required this.createdAt,
    this.lastLogin,
    required this.favoriteAccounts,
    this.adViewsRemaining = 20, // القيمة الافتراضية 20
  });

  // تحويل البيانات من Supabase إلى كائن UserModel
  factory UserModel.fromMap(Map<String, dynamic> map, dynamic userId) {
    return UserModel(
      id: userId is int ? userId : int.tryParse(userId.toString()) ?? 0,
      phoneNumber: map['phone_number'] ?? '',
      name: map['name'],
      secretCode: map['secret_code'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      lastLogin: map['last_login'] != null
          ? DateTime.parse(map['last_login'])
          : null,
      favoriteAccounts: map['favorite_accounts'] != null
          ? List<String>.from(map['favorite_accounts'])
          : [],
      adViewsRemaining: map['ad_views_remaining'] ?? 20,
    );
  }

  // تحويل كائن UserModel إلى Map لتخزينه في Supabase
  Map<String, dynamic> toMap() {
    return {
      'phone_number': phoneNumber,
      'name': name,
      'secret_code': secretCode,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
      'favorite_accounts': favoriteAccounts,
      'ad_views_remaining': adViewsRemaining,
    };
  }

  // نسخة معدلة من الكائن
  UserModel copyWith({
    int? id,
    String? phoneNumber,
    String? name,
    String? secretCode,
    DateTime? createdAt,
    DateTime? lastLogin,
    List<String>? favoriteAccounts,
    int? adViewsRemaining,
  }) {
    return UserModel(
      id: id ?? this.id,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      name: name ?? this.name,
      secretCode: secretCode ?? this.secretCode,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      favoriteAccounts: favoriteAccounts ?? this.favoriteAccounts,
      adViewsRemaining: adViewsRemaining ?? this.adViewsRemaining,
    );
  }
}
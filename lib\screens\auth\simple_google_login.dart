import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:wardly_app/screens/home_screen.dart';
import 'package:wardly_app/utils/app_theme.dart';

class SimpleGoogleLogin extends StatefulWidget {
  final String customerName;
  final String phoneNumber;

  const SimpleGoogleLogin({
    Key? key,
    required this.customerName,
    required this.phoneNumber,
  }) : super(key: key);

  @override
  State<SimpleGoogleLogin> createState() => _SimpleGoogleLoginState();
}

class _SimpleGoogleLoginState extends State<SimpleGoogleLogin> {
  bool _isLoading = false;
  String? _error;

  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // إنشاء Google Auth Provider
      GoogleAuthProvider googleProvider = GoogleAuthProvider();
      
      // إضافة scopes إضافية
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // تسجيل الدخول باستخدام popup للويب
      final UserCredential userCredential = await FirebaseAuth.instance.signInWithPopup(googleProvider);
      
      if (userCredential.user != null) {
        // حفظ بيانات المستخدم في Firestore
        await _saveUserData(userCredential.user!);
        
        // الانتقال إلى الشاشة الرئيسية
        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const HomeScreen()),
            (route) => false,
          );
        }
      }
    } catch (e) {
      setState(() {
        _error = 'خطأ في تسجيل الدخول: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveUserData(User user) async {
    try {
      final userDoc = FirebaseFirestore.instance.collection('users').doc(user.uid);
      
      // تنسيق رقم الهاتف
      String formattedPhone = widget.phoneNumber;
      if (!widget.phoneNumber.startsWith('+20') && widget.phoneNumber.startsWith('01')) {
        formattedPhone = '+20${widget.phoneNumber}';
      }

      final userData = {
        'email': user.email,
        'name': widget.customerName,
        'phoneNumber': formattedPhone,
        'photoUrl': user.photoURL,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLogin': FieldValue.serverTimestamp(),
        'favoriteAccounts': [],
        'authMethod': 'google',
      };

      await userDoc.set(userData, SetOptions(merge: true));
    } catch (e) {
      print('خطأ في حفظ بيانات المستخدم: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('تسجيل الدخول بـ Google'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة Google
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(50),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Icon(
                Icons.login,
                size: 50,
                color: Colors.red,
              ),
            ),
            
            const SizedBox(height: 32),
            
            // معلومات المستخدم
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                children: [
                  Text(
                    'بيانات التسجيل',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue[800],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Icon(Icons.person, color: Colors.blue),
                      const SizedBox(width: 12),
                      Text(
                        'الاسم: ${widget.customerName}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.phone, color: Colors.blue),
                      const SizedBox(width: 12),
                      Text(
                        'الهاتف: ${widget.phoneNumber}',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // زر تسجيل الدخول
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _signInWithGoogle,
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.login, size: 24),
              label: Text(
                _isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول بـ Google',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 3,
                minimumSize: const Size(double.infinity, 56),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // رسالة الخطأ
            if (_error != null)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Colors.red[600],
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _error!,
                        style: TextStyle(
                          color: Colors.red[600],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            const SizedBox(height: 32),
            
            // معلومات إضافية
            Text(
              'سيتم حفظ بياناتك بأمان مع حساب Google',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

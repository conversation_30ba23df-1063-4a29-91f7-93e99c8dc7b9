import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wardly_app/providers/supabase_auth_provider.dart';
import 'package:wardly_app/providers/transaction_provider.dart';
import 'package:wardly_app/screens/auth/login_screen.dart';
import 'package:wardly_app/screens/new_transaction_screen.dart';
import 'package:wardly_app/screens/transaction_history_screen.dart';

import 'package:wardly_app/utils/app_theme.dart';

import 'package:wardly_app/widgets/expandable_transaction_card.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  bool _showWelcomeCard = true;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();

    // إعداد انيميشن البطاقة المنزلقة
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // تبدأ من الجانب الأيمن
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    // استرداد سجل المعاملات عند تحميل الشاشة
    _fetchTransactions();
    // إظهار بطاقة الترحيب
    _showWelcomeCardAnimation();

    // إعداد تحديث دوري كل 30 ثانية
    _startPeriodicRefresh();

    // إضافة listener للتحديث عند تغيير المعاملات
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);
      transactionProvider.addListener(_onTransactionsChanged);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // إعادة تحميل المعاملات عند العودة للصفحة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchTransactions();
    });
  }

  // دالة للتحديث عند تغيير المعاملات
  void _onTransactionsChanged() {
    if (mounted) {
      _fetchTransactions();
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _refreshTimer?.cancel();
    // إزالة listener
    try {
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);
      transactionProvider.removeListener(_onTransactionsChanged);
    } catch (e) {
      // تجاهل الخطأ إذا كان المزود غير متاح
    }
    super.dispose();
  }

  // إظهار بطاقة الترحيب المنزلقة
  void _showWelcomeCardAnimation() {
    // تأخير بسيط للتأكد من تحميل الشاشة
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _slideController.forward();
        // إخفاء البطاقة بعد ثانيتين
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted && _showWelcomeCard) {
            _hideWelcomeCard();
          }
        });
      }
    });
  }

  // إخفاء بطاقة الترحيب
  void _hideWelcomeCard() {
    _slideController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _showWelcomeCard = false;
        });
      }
    });
  }

  // الحصول على رسالة الترحيب حسب عدد المعاملات
  Map<String, String> _getWelcomeMessage(String userName, int transactionCount) {
    if (transactionCount == 0) {
      return {
        'title': 'أهلاً بيك $userName',
        'subtitle': 'ابدأ أول عملية توريد معنا',
      };
    } else if (transactionCount == 1) {
      return {
        'title': 'أهلاً بيك $userName',
        'subtitle': 'لديك معاملة واحدة',
      };
    } else if (transactionCount <= 5) {
      return {
        'title': 'أهلاً بيك $userName',
        'subtitle': 'لديك $transactionCount معاملات',
      };
    } else if (transactionCount <= 10) {
      return {
        'title': 'أهلاً بيك $userName',
        'subtitle': '$transactionCount معاملة',
      };
    } else {
      return {
        'title': 'أهلاً بيك $userName',
        'subtitle': 'أكثر من $transactionCount معاملة',
      };
    }
  }

  // استرداد سجل المعاملات
  Future<void> _fetchTransactions() async {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
    final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

    if (authProvider.currentUser != null) {
      await transactionProvider.fetchUserTransactions(authProvider.currentUser!.id.toString());
    }
  }

  // بدء التحديث الدوري للمعاملات
  void _startPeriodicRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _fetchTransactions();
      }
    });
  }

  // فتح رابط الدعم الفني (واتساب)
  Future<void> _openSupportChat() async {
    try {
      // رقم الدعم الفني
      const phoneNumber = '201272897754'; // إزالة علامة + لتحسين التوافق
      const message = 'مرحباً، أحتاج مساعدة في تطبيق وردلي';
      final encodedMessage = Uri.encodeComponent(message);

      // محاولة فتح واتساب مباشرة أولاً
      final whatsappUri = Uri.parse('whatsapp://send?phone=$phoneNumber&text=$encodedMessage');

      if (await canLaunchUrl(whatsappUri)) {
        await launchUrl(whatsappUri, mode: LaunchMode.externalApplication);
        return;
      }

      // إذا فشل، محاولة استخدام رابط الويب
      final webUri = Uri.parse('https://wa.me/$phoneNumber?text=$encodedMessage');

      if (await canLaunchUrl(webUri)) {
        await launchUrl(webUri, mode: LaunchMode.externalApplication);
        return;
      }

      // إذا فشل كلاهما، محاولة فتح واتساب بدون رسالة
      final simpleUri = Uri.parse('whatsapp://send?phone=$phoneNumber');

      if (await canLaunchUrl(simpleUri)) {
        await launchUrl(simpleUri, mode: LaunchMode.externalApplication);
        return;
      }

      throw 'لا يمكن فتح تطبيق واتساب';

    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('لا يمكن فتح تطبيق واتساب. تأكد من تثبيت واتساب على جهازك.'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'نسخ الرقم',
            textColor: Colors.white,
            onPressed: () async {
              // نسخ رقم الهاتف للحافظة
              await Clipboard.setData(const ClipboardData(text: '+201272897754'));
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم نسخ رقم الدعم الفني'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            },
          ),
        ),
      );
    }
  }

  // عرض رسالة تأكيد تسجيل الخروج
  Future<void> _showLogoutConfirmation() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'تسجيل الخروج',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: const Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            style: TextStyle(fontSize: 16),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          actions: [
            // زر الإلغاء
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            // زر التأكيد
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );

    // إذا أكد المستخدم، قم بتسجيل الخروج
    if (result == true) {
      await _logout();
    }
  }

  // تسجيل الخروج
  Future<void> _logout() async {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
    await authProvider.signOut();

    if (!mounted) return;

    // عرض رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تسجيل الخروج بنجاح'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );

    // العودة إلى شاشة تسجيل الدخول
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (_) => const LoginScreen()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final transactionProvider = Provider.of<TransactionProvider>(context);
    final transactions = transactionProvider.transactions;

    return Scaffold(
      appBar: AppBar(
        title: const Text('وردلي'),
        actions: [
          // زر تسجيل الخروج
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _showLogoutConfirmation,
            tooltip: 'تسجيل الخروج',
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await _fetchTransactions();
          // إعادة تشغيل Timer للتحديث الدوري
          _refreshTimer?.cancel();
          _startPeriodicRefresh();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // بطاقة الترحيب المنزلقة
              if (_showWelcomeCard)
                SlideTransition(
                  position: _slideAnimation,
                  child: Consumer2<SupabaseAuthProvider, TransactionProvider>(
                    builder: (context, authProvider, transactionProvider, child) {
                      final currentUser = authProvider.currentUser;
                      final transactionCount = transactionProvider.transactions.length;

                      if (currentUser == null) return const SizedBox.shrink();

                      final welcomeMessage = _getWelcomeMessage(
                        currentUser.name ?? 'عميل وردلي',
                        transactionCount,
                      );

                      return Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        child: Card(
                          elevation: 8,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppTheme.primaryColor,
                                  AppTheme.accentColor,
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Row(
                              children: [
                                // أيقونة المستخدم
                                Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(30),
                                  ),
                                  child: Center(
                                    child: Text(
                                      currentUser.name?.substring(0, 1).toUpperCase() ?? 'و',
                                      style: const TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // نص الترحيب
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        welcomeMessage['title']!,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        welcomeMessage['subtitle']!,
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: Colors.white70,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // زر الإغلاق
                                IconButton(
                                  icon: const Icon(Icons.close, color: Colors.white70),
                                  onPressed: _hideWelcomeCard,
                                  tooltip: 'إغلاق',
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

              // بطاقة بدء التوريد
              Card(
                elevation: 4,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // أيقونة التوريد
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(40),
                        ),
                        child: const Icon(
                          Icons.account_balance_wallet,
                          size: 40,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(height: 16),
                      // عنوان البطاقة
                      const Text(
                        'توريد أموال طلبات',
                        style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      // وصف البطاقة
                      const Text(
                        'قم بتوريد أموالك إلى حساب طلبات بسهولة وأمان',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      // زر بدء التوريد
                      ElevatedButton.icon(
                        onPressed: () async {
                          // الحصول على المزودين قبل التنقل
                          final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
                          final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

                          // الانتقال لشاشة المعاملة الجديدة
                          final result = await Navigator.of(context).push(
                            MaterialPageRoute(builder: (_) => const NewTransactionScreen()),
                          );

                          // إعادة تحميل المعاملات عند العودة
                          if (result == true && mounted && authProvider.currentUser != null) {
                            await transactionProvider.fetchUserTransactions(authProvider.currentUser!.id.toString());
                            // إعادة تشغيل Timer للتحديث الدوري
                            _refreshTimer?.cancel();
                            _startPeriodicRefresh();
                          }
                        },
                        icon: const Icon(Icons.arrow_forward, size: 20),
                        label: const Text(
                          'ابدأ التوريد',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          minimumSize: const Size(double.infinity, 50),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
              // عنوان المعاملات
              const Text(
                'المعاملات',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              // قائمة المعاملات (آخر معاملتين فقط)
              if (transactionProvider.isLoading)
                const Center(child: CircularProgressIndicator())
              else if (transactions.isEmpty)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Text(
                      'لا توجد معاملات سابقة',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  ),
                )
              else
                Column(
                  children: [
                    // عرض آخر معاملتين فقط
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: transactions.length > 2 ? 2 : transactions.length,
                      itemBuilder: (context, index) {
                        return ExpandableTransactionCard(transaction: transactions[index]);
                      },
                    ),

                    // زر "سجل المعاملات" إذا كان هناك أكثر من معاملتين
                    if (transactions.length > 2) ...[
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (_) => const TransactionHistoryScreen(),
                              ),
                            );
                          },
                          icon: const Icon(Icons.history, color: Colors.white),
                          label: const Text(
                            'سجل المعاملات',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              const SizedBox(height: 24),
              // بطاقة الدعم الفني
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                child: InkWell(
                  onTap: _openSupportChat,
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // أيقونة الدعم
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.green.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: const Icon(
                            Icons.support_agent,
                            size: 24,
                            color: Colors.green,
                          ),
                        ),
                        const SizedBox(width: 16),
                        // نص الدعم
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'تحتاج مساعدة؟',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                'تواصل مع فريق الدعم الفني عبر واتساب',
                                style: TextStyle(fontSize: 14, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                        // أيقونة واتساب
                        const Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
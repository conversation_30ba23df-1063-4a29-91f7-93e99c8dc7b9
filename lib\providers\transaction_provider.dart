import 'package:flutter/material.dart';
import 'package:wardly_app/models/transaction.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class TransactionProvider extends ChangeNotifier {
  List<Transaction> _transactions = [];
  bool _isLoading = false;
  String? _error;

  // إعدادات Supabase
  static const String _supabaseUrl = 'https://vymeazhbvktmzillvnxc.supabase.co';
  static const String _supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.l6zkXbh_h_EfkIQ2FZyYDod6qafA24tfCbsNHcavmGE';

  // Getters
  List<Transaction> get transactions => _transactions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchUserTransactions(String userId) async {
    _setLoading(true);
    _error = null;

    try {
      // استرداد المعاملات من Supabase باستخدام HTTP
      final url = Uri.parse('$_supabaseUrl/rest/v1/transactions?user_id=eq.$userId&order=created_at.desc');
      final response = await http.get(
        url,
        headers: {
          'apikey': _supabaseKey,
          'Authorization': 'Bearer $_supabaseKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        // تحويل البيانات إلى قائمة Transaction
        _transactions = data.map((transactionData) {
          return Transaction(
            id: transactionData['id'].toString(),
            userId: transactionData['user_id'].toString(),
            talabatAccountNumber: transactionData['talabat_account_number'] ?? '',
            senderWalletNumber: transactionData['sender_wallet_number'] ?? '',
            amount: (transactionData['amount'] ?? 0).toDouble(),
            commission: (transactionData['commission'] ?? 0).toDouble(),
            totalAmount: (transactionData['total_amount'] ?? 0).toDouble(),
            walletType: transactionData['wallet_type'] ?? '',
            date: DateTime.parse(transactionData['created_at']),
            status: transactionData['status'] ?? 'جاري',

          );
        }).toList();
      } else {
        _error = 'خطأ في استرداد المعاملات: ${response.statusCode}';
        _transactions = [];
      }

    } catch (e) {
      _error = 'حدث خطأ أثناء استرداد المعاملات: $e';
      _transactions = []; // قائمة فارغة في حالة الخطأ
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> addTransaction({
    required String userId,
    required String talabatAccountNumber,
    required String senderWalletNumber,
    required double amount,
    required String walletType,
    required double commission,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      final totalAmount = amount + commission;
      final now = DateTime.now();

      // حفظ المعاملة في Supabase باستخدام HTTP
      final url = Uri.parse('$_supabaseUrl/rest/v1/transactions');
      final response = await http.post(
        url,
        headers: {
          'apikey': _supabaseKey,
          'Authorization': 'Bearer $_supabaseKey',
          'Content-Type': 'application/json',
          'Prefer': 'return=representation',
        },
        body: json.encode({
          'user_id': userId,
          'talabat_account_number': talabatAccountNumber,
          'sender_wallet_number': senderWalletNumber,
          'amount': amount,
          'commission': commission,
          'total_amount': totalAmount,
          'wallet_type': walletType,
          'status': 'جاري',
          'created_at': now.toIso8601String(),
        }),
      );

      if (response.statusCode == 201) {
        final responseData = json.decode(response.body)[0];

        // إنشاء Transaction object من الاستجابة
        final newTransaction = Transaction(
          id: responseData['id'].toString(),
          userId: responseData['user_id'].toString(),
          talabatAccountNumber: responseData['talabat_account_number'],
          senderWalletNumber: responseData['sender_wallet_number'],
          amount: responseData['amount'].toDouble(),
          commission: responseData['commission'].toDouble(),
          totalAmount: responseData['total_amount'].toDouble(),
          walletType: responseData['wallet_type'],
          date: DateTime.parse(responseData['created_at']),
          status: responseData['status'],

        );

      // إضافة المعاملة للقائمة المحلية
      _transactions.insert(0, newTransaction);
      notifyListeners();

      // إرسال إشعار للمدير عن العملية الجديدة
      try {
        await _sendTransactionNotification(newTransaction);
      } catch (e) {
        // فشل الإشعار لا يؤثر على نجاح المعاملة
        _error = null; // نتجاهل خطأ الإشعار
      }

      // إعادة تحميل المعاملات من قاعدة البيانات للتأكد من التحديث
      await _refreshTransactions(userId);

      return true;
      } else {
        _error = 'خطأ في حفظ المعاملة: ${response.statusCode}';
        return false;
      }
    } catch (e) {
      _error = 'حدث خطأ أثناء إضافة المعاملة: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Transaction? getTransactionById(String transactionId) {
    try {
      return _transactions.firstWhere((t) => t.id == transactionId);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void resetError() {
    _error = null;
    notifyListeners();
  }

  // إرسال إشعار المعاملة لبوت المعاملات المخصص
  Future<void> _sendTransactionNotification(Transaction transaction) async {
    try {
      // إرسال لبوت Wardly Notifications Bot (المعاملات)
      const botToken = '**********:AAH1beEuFqbOju-BXgydlQQeB6vuLU3ZDPw';
      const adminChatId = '*********';

      final message = '''
💰 [معاملة جديدة] تحتاج موافقة!

🆔 رقم المعاملة: ${transaction.id}
🏪 حساب طلبات: ${transaction.talabatAccountNumber}
💵 المبلغ: ${transaction.amount} جنيه
💳 نوع المحفظة: ${transaction.walletType}
📱 رقم المحفظة: ${transaction.senderWalletNumber}
💰 العمولة: ${transaction.commission} جنيه
💸 المجموع: ${transaction.totalAmount} جنيه
📅 الوقت: ${DateTime.now().toString().substring(0, 16)}


⏳ الحالة: جاري المراجعة
''';

      // إنشاء أزرار التحكم
      final keyboard = {
        'inline_keyboard': [
          [
            {
              'text': '✅ تم',
              'callback_data': 'approve_${transaction.id}'
            },
            {
              'text': '❌ مرفوض',
              'callback_data': 'reject_${transaction.id}'
            }
          ],
          [
            {
              'text': '⚙️ تعديل حالة المعاملة',
              'callback_data': 'edit_${transaction.id}'
            }
          ]
        ]
      };



      // إرسال رسالة النص مع الأزرار
      final url = Uri.parse('https://api.telegram.org/bot$botToken/sendMessage');
      await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'chat_id': adminChatId,
          'text': message,
          'reply_markup': keyboard,
        }),
      );
    } catch (e) {
      // تجاهل أخطاء الإشعار
    }
  }



  // إعادة تحميل المعاملات من قاعدة البيانات
  Future<void> _refreshTransactions(String userId) async {
    try {
      // استرداد المعاملات المحدثة من قاعدة البيانات
      final url = Uri.parse('$_supabaseUrl/rest/v1/transactions?user_id=eq.$userId&order=created_at.desc');
      final response = await http.get(
        url,
        headers: {
          'apikey': _supabaseKey,
          'Authorization': 'Bearer $_supabaseKey',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        // تحديث قائمة المعاملات
        _transactions = data.map((transactionData) {
          return Transaction(
            id: transactionData['id'].toString(),
            userId: transactionData['user_id'].toString(),
            talabatAccountNumber: transactionData['talabat_account_number'] ?? '',
            senderWalletNumber: transactionData['sender_wallet_number'] ?? '',
            amount: (transactionData['amount'] ?? 0).toDouble(),
            commission: (transactionData['commission'] ?? 0).toDouble(),
            totalAmount: (transactionData['total_amount'] ?? 0).toDouble(),
            walletType: transactionData['wallet_type'] ?? '',
            date: DateTime.parse(transactionData['created_at']),
            status: transactionData['status'] ?? 'جاري',

          );
        }).toList();

        notifyListeners();
      }
    } catch (e) {
      // تجاهل أخطاء إعادة التحميل
    }
  }


}

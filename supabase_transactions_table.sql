-- إنشاء جدول المعاملات في Supabase
-- نفذ هذا الكود في Supabase SQL Editor

CREATE TABLE IF NOT EXISTS transactions (
    id BIGSERIAL PRIMARY KEY,
    user_id TEXT NOT NULL,
    talabat_account_number TEXT NOT NULL,
    sender_wallet_number TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    commission DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    wallet_type TEXT NOT NULL,
    status TEXT DEFAULT 'جاري',
    proof_image_url TEXT, -- 📸 رابط صورة إثبات التحويل
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس للبحث السريع بـ user_id
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);

-- إنشاء فهرس للبحث السريع بـ created_at
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at DESC);

-- إنشاء فهرس للبحث السريع بـ status
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);

-- تفعيل Row Level Security
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- سياسة للسماح للمستخدمين برؤية معاملاتهم فقط
CREATE POLICY "Users can view their own transactions" ON transactions
    FOR SELECT USING (auth.uid()::text = user_id);

-- سياسة للسماح للمستخدمين بإنشاء معاملات جديدة
CREATE POLICY "Users can insert their own transactions" ON transactions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

-- سياسة للسماح للمستخدمين بتحديث معاملاتهم
CREATE POLICY "Users can update their own transactions" ON transactions
    FOR UPDATE USING (auth.uid()::text = user_id);

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق الدالة على جدول transactions
CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- إدراج بعض البيانات التجريبية (اختياري - يمكن حذفها)
-- INSERT INTO transactions (
--     user_id, 
--     talabat_account_number, 
--     sender_wallet_number, 
--     amount, 
--     commission, 
--     total_amount, 
--     wallet_type, 
--     status
-- ) VALUES 
-- ('1', '*********', '***********', 500.00, 2.50, 502.50, 'Vodafone Cash', 'تم'),
-- ('1', '*********', '***********', 300.00, 1.50, 301.50, 'Etisalat Cash', 'جاري');

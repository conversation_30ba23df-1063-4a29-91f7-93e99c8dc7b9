import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import 'dart:async';
import 'package:wardly_app/providers/supabase_auth_provider.dart';
import 'package:wardly_app/providers/transaction_provider.dart';
import 'package:wardly_app/screens/transaction_history_screen.dart';
import 'package:wardly_app/utils/app_theme.dart';




class NewTransactionScreen extends StatefulWidget {
  const NewTransactionScreen({Key? key}) : super(key: key);

  @override
  State<NewTransactionScreen> createState() => _NewTransactionScreenState();
}

class _NewTransactionScreenState extends State<NewTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountController = TextEditingController();
  final _amountController = TextEditingController();
  final _senderWalletController = TextEditingController();

  String _selectedWallet = 'Vodafone Cash';
  double _amount = 0;
  double _commission = 0;
  double _totalAmount = 0;
  bool _hasWatchedAd = false;



  // 🛡️ حماية من الضغط المتكرر
  bool _isSubmitting = false;
  Timer? _debounceTimer;

  final List<String> _wallets = [
    'Vodafone Cash',
    'Etisalat Cash',
    'Orange Money',
    'We Pay',
    'Instapay',
    'CIB Smart Wallet',
  ];

  final Map<String, String> _walletNumbers = {
    'Vodafone Cash': '***********',
    'Etisalat Cash': '***********',
    'Orange Money': '***********',
    'We Pay': '***********',
    'Instapay': 'instapayuser@bank',
    'CIB Smart Wallet': '***********',
  };

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_calculateCommission);
    _accountController.addListener(_updateFormState);
    _senderWalletController.addListener(_updateFormState);

    // تعيين القيمة الافتراضية لرقم المحفظة (فارغ)
    _senderWalletController.text = '';

    _loadAdUsageCount(); // تحميل عدد مرات الاستخدام
  }

  // تحميل عدد مرات استخدام الإعلان من قاعدة البيانات
  Future<void> _loadAdUsageCount() async {
    // في التطبيق الحقيقي، يجب تحميل هذا من Firebase أو SharedPreferences
    // هنا نقوم بمحاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    // لا حاجة لتعيين عدد المشاهدات - سيتم أخذه من المستخدم
  }

  @override
  void dispose() {
    _accountController.dispose();
    _amountController.dispose();
    _senderWalletController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // 🛡️ دالة wrapper مع حماية من الضغط المتكرر
  void _handleSubmitTransaction() {
    // إلغاء أي timer سابق
    _debounceTimer?.cancel();

    // إنشاء timer جديد مع تأخير 500ms
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _submitTransaction();
    });
  }









  void _calculateCommission() {
    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      setState(() {
        _amount = 0;
        _commission = 0;
        _totalAmount = 0;
      });
      return;
    }

    try {
      final amount = double.parse(amountText);

      // حساب العمولة: 0.5% من المبلغ بحد أقصى 5 جنيه
      double commission = amount * 0.005;

      // إذا شاهد الإعلان، العمولة = 0، وإلا الحد الأقصى 5 جنيه
      if (_hasWatchedAd) {
        commission = 0;
      } else if (commission > 5) {
        commission = 5;
      }

      final totalAmount = amount + commission;

      setState(() {
        _amount = amount;
        _commission = commission;
        _totalAmount = totalAmount;
      });
    } catch (e) {
      setState(() {
        _amount = 0;
        _commission = 0;
        _totalAmount = 0;
      });
    }
  }

  // تحديث حالة النموذج عند تغيير النصوص
  void _updateFormState() {
    setState(() {
      // تحديث الحالة لإعادة بناء الواجهة وتحديث حالة الزر
    });
  }

  // التحقق من صحة جميع الحقول
  bool get _isFormValid {
    final walletNumber = _senderWalletController.text.trim();
    return _accountController.text.trim().isNotEmpty &&
           walletNumber.isNotEmpty &&
           walletNumber.length == 11 && // 📱 11 رقم كامل
           walletNumber.startsWith('01') && // يجب أن يبدأ بـ 01
           RegExp(r'^[0-9]+$').hasMatch(walletNumber) &&
           _amount > 0;
  }

  // مشاهدة الإعلان لإلغاء العمولة
  Future<void> _watchAd() async {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);

    // التحقق من عدد مرات الاستخدام
    if (authProvider.currentUser == null || authProvider.currentUser!.adViewsRemaining <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⚠️ لقد استنفدت عدد مرات مشاهدة الإعلان المسموحة'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // محاكاة مشاهدة إعلان لمدة 30 ثانية
    showDialog(
      context: context,
      barrierDismissible: false, // لا يمكن إغلاق الحوار بالضغط خارجه
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // منع الإغلاق بزر الرجوع
          child: AlertDialog(
            title: const Text(
              'مشاهدة الإعلان',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text(
                  'يرجى مشاهدة الإعلان كاملاً لإلغاء العمولة',
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'لا يمكن تخطي الإعلان',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );

    // محاكاة مدة الإعلان (30 ثانية)
    await Future.delayed(const Duration(seconds: 30));

    if (!mounted) return;

    // إغلاق حوار الإعلان
    Navigator.of(context).pop();

    final auth = Provider.of<SupabaseAuthProvider>(context, listen: false);

    // تقليل عدد مرات المشاهدة في قاعدة البيانات
    final success = await auth.decrementAdViews();

    if (success) {
      // تحديث حالة مشاهدة الإعلان
      setState(() {
        _hasWatchedAd = true;
      });

      // إعادة حساب العمولة
      _calculateCommission();

      // عرض رسالة نجاح مع عدد المرات المتبقية
      final remainingUses = auth.currentUser?.adViewsRemaining ?? 0;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            remainingUses > 0
              ? '🎉 تم إلغاء العمولة بنجاح! المتبقي: $remainingUses مرة'
              : '🎉 تم إلغاء العمولة! هذه آخر مرة يمكنك استخدام الإعلان',
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 4),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ فشل في تحديث عدد المشاهدات'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }



  Future<void> _submitTransaction() async {
    // 🛡️ منع الضغط المتكرر
    if (_isSubmitting) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⏳ جاري إرسال المعاملة، يرجى الانتظار...'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) return;

    // 🛡️ تفعيل حالة الإرسال
    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

      if (authProvider.currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يرجى تسجيل الدخول أولاً')),
        );
        return;
      }



    final success = await transactionProvider.addTransaction(
      userId: authProvider.currentUser!.id.toString(),
      talabatAccountNumber: _accountController.text.trim(),
      amount: _amount,
      walletType: _selectedWallet,
      senderWalletNumber: _senderWalletController.text.trim(),
      commission: _commission,
    );

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال طلب التوريد بنجاح'),
          backgroundColor: AppTheme.successColor,
        ),
      );

      // الانتقال إلى صفحة سجل المعاملات
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const TransactionHistoryScreen(),
        ),
      );
    } else if (transactionProvider.error != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(transactionProvider.error!)),
      );
      transactionProvider.resetError();
    }

    } catch (e) {
      // 🛡️ معالجة الأخطاء غير المتوقعة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // 🛡️ إعادة تعيين حالة الإرسال في جميع الحالات
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Future<void> _openInstapay() async {
    final Uri uri = Uri.parse('instapay://pay?to=${_walletNumbers['Instapay']}&amount=$_amount');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تعذر فتح تطبيق Instapay')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final transactionProvider = Provider.of<TransactionProvider>(context);
    final walletNumber = _walletNumbers[_selectedWallet] ?? '';

    return Scaffold(
      appBar: AppBar(
        title: const Text('توريد جديد'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(40),
                    ),
                    child: const Icon(Icons.account_balance_wallet, size: 40, color: AppTheme.primaryColor),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'توريد أموال إلى حساب طلبات',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'أدخل بيانات التوريد لإتمام العملية',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                TextFormField(
                  controller: _accountController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  decoration: InputDecoration(
                    labelText: 'رقم حساب طلبات',
                    hintText: 'ID Talabat',
                    prefixIcon: const Icon(Icons.account_circle),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                    ),
                  ),
                  validator: (value) => value == null || value.isEmpty ? 'يرجى إدخال رقم الحساب' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _senderWalletController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(11),
                  ],
                  decoration: InputDecoration(
                    labelText: 'رقم المحفظة المرسلة',
                    hintText: '01xxxxxxxxx',
                    prefixIcon: const Icon(Icons.phone_android),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رقم المحفظة';
                    }
                    if (value.length != 11) {
                      return 'رقم المحفظة يجب أن يكون 11 رقم';
                    }
                    if (!value.startsWith('01')) {
                      return 'رقم المحفظة يجب أن يبدأ بـ 01';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ (جنيه)',
                    prefixIcon: Icon(Icons.attach_money),
                  ),
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  validator: (value) {
                    final amount = double.tryParse(value ?? '');
                    if (amount == null || amount <= 0) return 'يرجى إدخال مبلغ صحيح';
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedWallet,
                  decoration: const InputDecoration(
                    labelText: 'المحفظة الإلكترونية',
                    prefixIcon: Icon(Icons.account_balance),
                  ),
                  items: _wallets.map((wallet) => DropdownMenuItem(value: wallet, child: Text(wallet))).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedWallet = value);
                    }
                  },
                ),
                const SizedBox(height: 16),


                if (walletNumber.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.shade300, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.shade100,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.account_balance_wallet,
                              color: Colors.green.shade700, size: 24),
                            const SizedBox(width: 8),
                            Text(
                              'رقم التحويل إلى $_selectedWallet',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.green.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.green.shade200),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  walletNumber,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1.5,
                                    color: Colors.black87,
                                  ),
                                  textDirection: TextDirection.ltr,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.green.shade600,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.copy, color: Colors.white),
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(text: walletNumber));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: const Row(
                                          children: [
                                            Icon(Icons.check_circle, color: Colors.white),
                                            SizedBox(width: 8),
                                            Text('تم نسخ رقم المحفظة بنجاح'),
                                          ],
                                        ),
                                        backgroundColor: Colors.green,
                                        behavior: SnackBarBehavior.floating,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                      ),
                                    );
                                  },
                                  tooltip: 'نسخ الرقم',
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(Icons.info_outline,
                              color: Colors.green.shade600, size: 16),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                'اضغط على أيقونة النسخ لنسخ الرقم',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.green.shade600,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
                if (_selectedWallet == 'Instapay')
                  TextButton.icon(
                    icon: const Icon(Icons.open_in_new),
                    label: const Text('افتح تطبيق Instapay لإتمام التحويل'),
                    onPressed: _openInstapay,
                  ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '💰 نسبة العمولة:',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '• نصف جنيه على كل 100 جنيه (0.5%)',
                        style: TextStyle(fontSize: 12),
                      ),
                      const Text(
                        '• أقصى عمولة: 5 جنيه فقط',
                        style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.green),
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          return Text(
                            '• مشاهدة الإعلان: $remainingViews مرة متبقية',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: remainingViews <= 0 ? Colors.red : Colors.blue,
                            ),
                          );
                        },
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          if (_commission > 0 && !_hasWatchedAd && remainingViews > 0) {
                            return Column(
                              children: [
                        const SizedBox(height: 8),
                        const Divider(),
                        const Text(
                          '🎬 شاهد إعلان قصير وألغِ العمولة نهائياً!',
                          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _watchAd,
                            icon: const Icon(Icons.play_circle_fill, color: Colors.white),
                            label: const Text(
                              'شاهد الإعلان (30 ثانية)',
                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          if (_commission > 0 && !_hasWatchedAd && remainingViews <= 0) {
                            return Column(
                              children: [
                        const SizedBox(height: 8),
                        const Divider(),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.red.shade100,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Row(
                            children: [
                              Icon(Icons.warning, color: Colors.red, size: 20),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'لقد استنفدت عدد مرات مشاهدة الإعلان المسموحة. يجب دفع العمولة.',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                                ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      if (_hasWatchedAd) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.green, size: 20),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'تم إلغاء العمولة! يمكنك التوريد بدون عمولة',
                                  style: TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),



                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبلغ:'),
                          Text('${_amount.toStringAsFixed(2)} جنيه', style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('العمولة (0.5%):'),
                          Text('${_commission.toStringAsFixed(2)} جنيه', style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('الإجمالي:', style: TextStyle(fontWeight: FontWeight.bold)),
                          Text('${_totalAmount.toStringAsFixed(2)} جنيه',
                            style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 16),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: _isFormValid && !transactionProvider.isLoading && !_isSubmitting ? _handleSubmitTransaction : null,
                  icon: (transactionProvider.isLoading || _isSubmitting)
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.check_circle, size: 20),
                  label: Text(
                    (transactionProvider.isLoading || _isSubmitting) ? 'جاري المعالجة...' : 'تم التحويل',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    minimumSize: const Size(double.infinity, 50),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: const Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.warning_amber, color: Colors.orange, size: 16),
                          SizedBox(width: 8),
                          Text(
                            'تنبيه مهم:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        'تأكد من تحويل المبلغ الإجمالي (شامل العمولة) قبل الضغط على "تم التحويل"',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

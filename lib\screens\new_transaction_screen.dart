import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import 'dart:async';
import 'package:wardly_app/providers/supabase_auth_provider.dart';
import 'package:wardly_app/providers/transaction_provider.dart';
import 'package:wardly_app/utils/app_theme.dart';
import 'package:wardly_app/supabase_config.dart';

// مُنسق مخصص لإجبار رقم الهاتف البدء بـ 01
class _PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    String newText = newValue.text;

    // إذا كان النص فارغ، ابدأ بـ 01
    if (newText.isEmpty) {
      return const TextEditingValue(
        text: '01',
        selection: TextSelection.collapsed(offset: 2),
      );
    }

    // إذا لم يبدأ بـ 01، أجبره على البدء بـ 01
    if (!newText.startsWith('01')) {
      if (newText.startsWith('0')) {
        newText = '01' + newText.substring(1);
      } else if (newText.startsWith('1')) {
        newText = '01' + newText;
      } else {
        newText = '01' + newText;
      }
    }

    // تحديد موضع المؤشر
    int offset = newText.length;
    if (offset > 11) {
      newText = newText.substring(0, 11);
      offset = 11;
    }

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: offset),
    );
  }
}


class NewTransactionScreen extends StatefulWidget {
  const NewTransactionScreen({Key? key}) : super(key: key);

  @override
  State<NewTransactionScreen> createState() => _NewTransactionScreenState();
}

class _NewTransactionScreenState extends State<NewTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _accountController = TextEditingController();
  final _amountController = TextEditingController();
  final _senderWalletController = TextEditingController();

  String _selectedWallet = 'Vodafone Cash';
  double _amount = 0;
  double _commission = 0;
  double _totalAmount = 0;
  bool _hasWatchedAd = false;

  // 📸 متغيرات الصورة
  File? _proofImage;
  final ImagePicker _picker = ImagePicker();
  bool _isUploadingImage = false;

  // 🛡️ حماية من الضغط المتكرر
  bool _isSubmitting = false;
  Timer? _debounceTimer;

  final List<String> _wallets = [
    'Vodafone Cash',
    'Etisalat Cash',
    'Orange Money',
    'We Pay',
    'Instapay',
    'CIB Smart Wallet',
  ];

  final Map<String, String> _walletNumbers = {
    'Vodafone Cash': '***********',
    'Etisalat Cash': '***********',
    'Orange Money': '***********',
    'We Pay': '***********',
    'Instapay': 'instapayuser@bank',
    'CIB Smart Wallet': '***********',
  };

  @override
  void initState() {
    super.initState();
    _amountController.addListener(_calculateCommission);
    _accountController.addListener(_updateFormState);
    _senderWalletController.addListener(_updateFormState);

    // تعيين القيمة الافتراضية لرقم المحفظة
    _senderWalletController.text = '01';

    _loadAdUsageCount(); // تحميل عدد مرات الاستخدام
  }

  // تحميل عدد مرات استخدام الإعلان من قاعدة البيانات
  Future<void> _loadAdUsageCount() async {
    // في التطبيق الحقيقي، يجب تحميل هذا من Firebase أو SharedPreferences
    // هنا نقوم بمحاكاة تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    // لا حاجة لتعيين عدد المشاهدات - سيتم أخذه من المستخدم
  }

  @override
  void dispose() {
    _accountController.dispose();
    _amountController.dispose();
    _senderWalletController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // 🛡️ دالة wrapper مع حماية من الضغط المتكرر
  void _handleSubmitTransaction() {
    // إلغاء أي timer سابق
    _debounceTimer?.cancel();

    // إنشاء timer جديد مع تأخير 500ms
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _submitTransaction();
    });
  }

  // 🔐 التحقق من حالة المصادقة وتسجيل الدخول في Supabase إذا لزم الأمر
  Future<bool> _ensureSupabaseAuth() async {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);

    // إذا كان المستخدم مسجل دخول في التطبيق ولكن ليس في Supabase
    if (authProvider.isAuthenticated && authProvider.currentUser != null) {
      final supabaseUser = SupabaseConfig.client.auth.currentUser;

      if (supabaseUser == null) {
        try {
          // تسجيل دخول مجهول في Supabase للسماح برفع الصور
          await SupabaseConfig.client.auth.signInAnonymously();
          print('✅ تم تسجيل دخول مجهول في Supabase');
          return true;
        } catch (e) {
          print('❌ فشل في تسجيل الدخول المجهول: $e');
          return false;
        }
      }
      return true;
    }

    return false;
  }

  // 📸 دالة اختيار الصورة
  Future<void> _pickImage() async {
    try {
      // التحقق من تسجيل الدخول أولاً
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);

      // طباعة معلومات التشخيص
      print('🔍 فحص تسجيل الدخول:');
      print('   - authProvider.isAuthenticated: ${authProvider.isAuthenticated}');
      print('   - authProvider.currentUser: ${authProvider.currentUser}');
      print('   - authProvider.currentUser?.id: ${authProvider.currentUser?.id}');

      // فحص حالة المصادقة في التطبيق فقط
      if (!authProvider.isAuthenticated || authProvider.currentUser == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ يرجى تسجيل الدخول أولاً لرفع الصور'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      print('✅ المستخدم مسجل دخول، المتابعة لاختيار الصورة...');

      // عرض خيارات اختيار الصورة أولاً
      final source = await _showImageSourceDialog();
      if (source == null) return;

      // طلب الأذونات حسب المصدر المختار
      bool hasPermission = false;

      if (source == ImageSource.camera) {
        final cameraStatus = await Permission.camera.status;
        if (cameraStatus.isDenied) {
          final result = await Permission.camera.request();
          hasPermission = result.isGranted;
        } else {
          hasPermission = cameraStatus.isGranted;
        }

        if (!hasPermission) {
          if (mounted) {
            _showPermissionDialog('الكاميرا');
          }
          return;
        }
      } else {
        // للمعرض - فحص أذونات مختلفة حسب إصدار Android
        PermissionStatus storageStatus = await Permission.photos.status;

        if (storageStatus.isDenied || storageStatus.isPermanentlyDenied) {
          storageStatus = await Permission.photos.request();
        }

        // إذا لم تنجح photos، جرب storage
        if (!storageStatus.isGranted) {
          storageStatus = await Permission.storage.status;
          if (storageStatus.isDenied) {
            storageStatus = await Permission.storage.request();
          }
        }

        hasPermission = storageStatus.isGranted;

        if (!hasPermission) {
          if (mounted) {
            _showPermissionDialog('المعرض');
          }
          return;
        }
      }

      // اختيار الصورة
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _proofImage = File(image.path);
        });
        _updateFormState(); // تحديث حالة النموذج

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ تم اختيار الصورة بنجاح'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // 📸 عرض حوار طلب الأذونات
  void _showPermissionDialog(String permissionType) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إذن مطلوب'),
          content: Text(
            'يحتاج التطبيق إلى إذن الوصول إلى $permissionType لرفع صورة إثبات التحويل.\n\nيرجى الذهاب إلى الإعدادات والسماح بالوصول.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('فتح الإعدادات'),
            ),
          ],
        );
      },
    );
  }

  // 📸 عرض خيارات مصدر الصورة
  Future<ImageSource?> _showImageSourceDialog() async {
    return await showDialog<ImageSource>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: [
              Icon(Icons.add_a_photo, color: AppTheme.primaryColor),
              SizedBox(width: 12),
              Text(
                'اختر مصدر الصورة',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 8),
              // خيار الكاميرا
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(bottom: 12),
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.of(context).pop(ImageSource.camera),
                  icon: const Icon(Icons.camera_alt, size: 24),
                  label: const Text(
                    'الكاميرا',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              // خيار المعرض
              Container(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.of(context).pop(ImageSource.gallery),
                  icon: const Icon(Icons.photo_library, size: 24),
                  label: const Text(
                    'المعرض',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey.shade600,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text(
                'إلغاء',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ],
        );
      },
    );
  }



  void _calculateCommission() {
    final amountText = _amountController.text.trim();
    if (amountText.isEmpty) {
      setState(() {
        _amount = 0;
        _commission = 0;
        _totalAmount = 0;
      });
      return;
    }

    try {
      final amount = double.parse(amountText);

      // حساب العمولة: 0.5% من المبلغ بحد أقصى 5 جنيه
      double commission = amount * 0.005;

      // إذا شاهد الإعلان، العمولة = 0، وإلا الحد الأقصى 5 جنيه
      if (_hasWatchedAd) {
        commission = 0;
      } else if (commission > 5) {
        commission = 5;
      }

      final totalAmount = amount + commission;

      setState(() {
        _amount = amount;
        _commission = commission;
        _totalAmount = totalAmount;
      });
    } catch (e) {
      setState(() {
        _amount = 0;
        _commission = 0;
        _totalAmount = 0;
      });
    }
  }

  // تحديث حالة النموذج عند تغيير النصوص
  void _updateFormState() {
    setState(() {
      // تحديث الحالة لإعادة بناء الواجهة وتحديث حالة الزر
    });
  }

  // التحقق من صحة جميع الحقول
  bool get _isFormValid {
    final phoneNumber = _senderWalletController.text.trim();
    return _accountController.text.trim().isNotEmpty &&
           phoneNumber.isNotEmpty &&
           phoneNumber.length == 11 &&
           phoneNumber.startsWith('01') && // 📱 يجب أن يبدأ بـ 01
           RegExp(r'^[0-9]+$').hasMatch(phoneNumber) &&
           _amount > 0 &&
           _proofImage != null; // 📸 إجبار رفع صورة إثبات التحويل
  }

  // مشاهدة الإعلان لإلغاء العمولة
  Future<void> _watchAd() async {
    final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);

    // التحقق من عدد مرات الاستخدام
    if (authProvider.currentUser == null || authProvider.currentUser!.adViewsRemaining <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⚠️ لقد استنفدت عدد مرات مشاهدة الإعلان المسموحة'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    // محاكاة مشاهدة إعلان لمدة 30 ثانية
    showDialog(
      context: context,
      barrierDismissible: false, // لا يمكن إغلاق الحوار بالضغط خارجه
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // منع الإغلاق بزر الرجوع
          child: AlertDialog(
            title: const Text(
              'مشاهدة الإعلان',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text(
                  'يرجى مشاهدة الإعلان كاملاً لإلغاء العمولة',
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),
                Text(
                  'لا يمكن تخطي الإعلان',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      },
    );

    // محاكاة مدة الإعلان (30 ثانية)
    await Future.delayed(const Duration(seconds: 30));

    if (!mounted) return;

    // إغلاق حوار الإعلان
    Navigator.of(context).pop();

    final auth = Provider.of<SupabaseAuthProvider>(context, listen: false);

    // تقليل عدد مرات المشاهدة في قاعدة البيانات
    final success = await auth.decrementAdViews();

    if (success) {
      // تحديث حالة مشاهدة الإعلان
      setState(() {
        _hasWatchedAd = true;
      });

      // إعادة حساب العمولة
      _calculateCommission();

      // عرض رسالة نجاح مع عدد المرات المتبقية
      final remainingUses = auth.currentUser?.adViewsRemaining ?? 0;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            remainingUses > 0
              ? '🎉 تم إلغاء العمولة بنجاح! المتبقي: $remainingUses مرة'
              : '🎉 تم إلغاء العمولة! هذه آخر مرة يمكنك استخدام الإعلان',
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 4),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ فشل في تحديث عدد المشاهدات'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 📸 دالة رفع الصورة إلى Supabase Storage
  Future<String?> _uploadProofImage(File imageFile) async {
    try {
      // الحصول على معرف المستخدم الحالي
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      final userId = authProvider.currentUser?.id;

      // طباعة معلومات التشخيص
      print('🔍 فحص المستخدم في دالة الرفع:');
      print('   - userId: $userId');
      print('   - authProvider.currentUser: ${authProvider.currentUser}');
      print('   - Supabase Auth User: ${SupabaseConfig.client.auth.currentUser}');

      if (userId == null) {
        throw Exception('المستخدم غير مسجل دخول');
      }

      // التأكد من تسجيل الدخول في Supabase Auth
      if (SupabaseConfig.client.auth.currentUser == null) {
        try {
          // تسجيل دخول مجهول مع معرف مخصص
          await SupabaseConfig.client.auth.signInAnonymously();
          print('✅ تم تسجيل دخول مجهول في Supabase Auth');
          print('   - معرف Supabase الجديد: ${SupabaseConfig.client.auth.currentUser?.id}');
        } catch (e) {
          print('❌ فشل في تسجيل الدخول في Supabase Auth: $e');
          throw Exception('فشل في تهيئة نظام رفع الصور');
        }
      } else {
        print('✅ المستخدم مسجل دخول مسبق<|im_start|> في Supabase Auth');
        print('   - معرف Supabase الحالي: ${SupabaseConfig.client.auth.currentUser?.id}');
      }

      // إنشاء اسم فريد للصورة مع مجلد المستخدم (مطلوب للسياسات)
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final random = DateTime.now().microsecond;
      final supabaseUserId = SupabaseConfig.client.auth.currentUser?.id ?? userId.toString();
      final fileName = '$supabaseUserId/proof_${timestamp}_$random.jpg';

      print('📁 مسار الصورة: $fileName');
      print('   - معرف التطبيق: $userId');
      print('   - معرف Supabase: $supabaseUserId');

      // محاولة رفع الصورة مع معالجة أفضل للأخطاء
      try {
        // قراءة بيانات الصورة
        final imageBytes = await imageFile.readAsBytes();

        // محاولة رفع الصورة بطرق مختلفة
        try {
          // الطريقة الأولى: uploadBinary
          await SupabaseConfig.client.storage
              .from('transaction-proofs')
              .uploadBinary(fileName, imageBytes);
        } catch (e1) {
          try {
            // الطريقة الثانية: upload مع File
            await SupabaseConfig.client.storage
                .from('transaction-proofs')
                .upload(fileName, imageFile);
          } catch (e2) {
            // إذا فشلت الطريقتان، ارمي الخطأ الأول
            throw e1;
          }
        }

        // الحصول على الرابط العام للصورة
        final publicUrl = SupabaseConfig.client.storage
            .from('transaction-proofs')
            .getPublicUrl(fileName);

        // التحقق من صحة الرابط
        if (publicUrl.isEmpty) {
          throw Exception('فشل في الحصول على رابط الصورة');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('✅ تم رفع الصورة بنجاح'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
              action: SnackBarAction(
                label: 'عرض',
                textColor: Colors.white,
                onPressed: () {
                  // يمكن إضافة عرض الصورة هنا لاحق<|im_start|>
                },
              ),
            ),
          );
        }

        return publicUrl;

      } catch (storageError) {
        // معالجة أخطاء Supabase Storage المحددة
        String errorMessage = 'فشل في رفع الصورة';

        if (storageError.toString().contains('403') || storageError.toString().contains('Unauthorized')) {
          errorMessage = 'غير مصرح برفع الصورة. يرجى المحاولة مرة أخرى';
        } else if (storageError.toString().contains('413') || storageError.toString().contains('size')) {
          errorMessage = 'حجم الصورة كبير جداً. يرجى اختيار صورة أصغر';
        } else if (storageError.toString().contains('422')) {
          errorMessage = 'نوع الملف غير مدعوم. يرجى اختيار صورة صحيحة';
        } else {
          errorMessage = 'خطأ في الخادم: ${storageError.toString()}';
        }

        throw Exception(errorMessage);
      }

    } catch (e) {
      String errorMessage = 'فشل في رفع الصورة';

      // تحديد رسالة الخطأ حسب النوع
      if (e.toString().contains('Unauthorized') || e.toString().contains('403')) {
        errorMessage = 'غير مصرح برفع الصورة. تحقق من الاتصال بالإنترنت';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'مشكلة في الاتصال. يرجى التحقق من الإنترنت';
      } else if (e.toString().contains('size') || e.toString().contains('large')) {
        errorMessage = 'حجم الصورة كبير جداً. يرجى اختيار صورة أصغر';
      } else if (e is Exception) {
        errorMessage = e.toString().replaceAll('Exception: ', '');
      }

      // عرض رسالة خطأ للمستخدم
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ $errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickImage(),
            ),
          ),
        );
      }
      return null;
    }
  }

  Future<void> _submitTransaction() async {
    // 🛡️ منع الضغط المتكرر
    if (_isSubmitting) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('⏳ جاري إرسال المعاملة، يرجى الانتظار...'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) return;

    // 🛡️ تفعيل حالة الإرسال
    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<SupabaseAuthProvider>(context, listen: false);
      final transactionProvider = Provider.of<TransactionProvider>(context, listen: false);

      if (authProvider.currentUser == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('يرجى تسجيل الدخول أولاً')),
        );
        return;
      }

    // 📸 رفع الصورة إذا كانت موجودة
    String? imageUrl;
    if (_proofImage != null) {
      setState(() {
        _isUploadingImage = true;
      });

      imageUrl = await _uploadProofImage(_proofImage!);

      setState(() {
        _isUploadingImage = false;
      });

      // إذا فشل رفع الصورة، توقف ولا تكمل
      if (imageUrl == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ يجب رفع صورة إثبات التحويل لإرسال المعاملة'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
        return; // توقف هنا ولا تكمل إرسال المعاملة
      }
    }

    final success = await transactionProvider.addTransaction(
      userId: authProvider.currentUser!.id.toString(),
      talabatAccountNumber: _accountController.text.trim(),
      amount: _amount,
      walletType: _selectedWallet,
      senderWalletNumber: _senderWalletController.text.trim(),
      commission: _commission,
      proofImageUrl: imageUrl, // 📸 جديد
    );

    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال طلب التوريد بنجاح'),
          backgroundColor: AppTheme.successColor,
        ),
      );
      Navigator.of(context).pop(true); // إرجاع true للإشارة للنجاح
    } else if (transactionProvider.error != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(transactionProvider.error!)),
      );
      transactionProvider.resetError();
    }

    } catch (e) {
      // 🛡️ معالجة الأخطاء غير المتوقعة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ غير متوقع: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // 🛡️ إعادة تعيين حالة الإرسال في جميع الحالات
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Future<void> _openInstapay() async {
    final Uri uri = Uri.parse('instapay://pay?to=${_walletNumbers['Instapay']}&amount=$_amount');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تعذر فتح تطبيق Instapay')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final transactionProvider = Provider.of<TransactionProvider>(context);
    final walletNumber = _walletNumbers[_selectedWallet] ?? '';

    return Scaffold(
      appBar: AppBar(
        title: const Text('توريد جديد'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(40),
                    ),
                    child: const Icon(Icons.account_balance_wallet, size: 40, color: AppTheme.primaryColor),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'توريد أموال إلى حساب طلبات',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                const Text(
                  'أدخل بيانات التوريد لإتمام العملية',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                TextFormField(
                  controller: _accountController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  decoration: const InputDecoration(
                    labelText: 'رقم حساب طلبات',
                    prefixIcon: Icon(Icons.account_circle),
                  ),
                  validator: (value) => value == null || value.isEmpty ? 'يرجى إدخال رقم الحساب' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _senderWalletController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(11),
                    _PhoneNumberFormatter(), // مُنسق مخصص لإجبار البدء بـ 01
                  ],
                  decoration: const InputDecoration(
                    labelText: 'رقم المحفظة المُرسلة',
                    prefixIcon: Icon(Icons.phone_android),
                    counterText: '',
                    hintText: '01xxxxxxxxx',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رقم المحفظة';
                    }
                    if (value.length != 11) {
                      return 'رقم الهاتف يجب أن يكون 11 رقم بالضبط';
                    }
                    if (!value.startsWith('01')) {
                      return 'رقم الهاتف يجب أن يبدأ بـ 01';
                    }
                    if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                      return 'يرجى إدخال أرقام فقط';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'المبلغ (جنيه)',
                    prefixIcon: Icon(Icons.attach_money),
                  ),
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  validator: (value) {
                    final amount = double.tryParse(value ?? '');
                    if (amount == null || amount <= 0) return 'يرجى إدخال مبلغ صحيح';
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedWallet,
                  decoration: const InputDecoration(
                    labelText: 'المحفظة الإلكترونية',
                    prefixIcon: Icon(Icons.account_balance),
                  ),
                  items: _wallets.map((wallet) => DropdownMenuItem(value: wallet, child: Text(wallet))).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => _selectedWallet = value);
                    }
                  },
                ),
                const SizedBox(height: 16),
                if (walletNumber.isNotEmpty) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green.shade300, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.green.shade100,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.account_balance_wallet,
                              color: Colors.green.shade700, size: 24),
                            const SizedBox(width: 8),
                            Text(
                              'رقم التحويل إلى $_selectedWallet',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.green.shade700,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.green.shade200),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  walletNumber,
                                  style: const TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1.5,
                                    color: Colors.black87,
                                  ),
                                  textDirection: TextDirection.ltr,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.green.shade600,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.copy, color: Colors.white),
                                  onPressed: () {
                                    Clipboard.setData(ClipboardData(text: walletNumber));
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: const Row(
                                          children: [
                                            Icon(Icons.check_circle, color: Colors.white),
                                            SizedBox(width: 8),
                                            Text('تم نسخ رقم المحفظة بنجاح'),
                                          ],
                                        ),
                                        backgroundColor: Colors.green,
                                        behavior: SnackBarBehavior.floating,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(10),
                                        ),
                                      ),
                                    );
                                  },
                                  tooltip: 'نسخ الرقم',
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(Icons.info_outline,
                              color: Colors.green.shade600, size: 16),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                'اضغط على أيقونة النسخ لنسخ الرقم',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.green.shade600,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
                if (_selectedWallet == 'Instapay')
                  TextButton.icon(
                    icon: const Icon(Icons.open_in_new),
                    label: const Text('افتح تطبيق Instapay لإتمام التحويل'),
                    onPressed: _openInstapay,
                  ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '💰 نسبة العمولة:',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '• نصف جنيه على كل 100 جنيه (0.5%)',
                        style: TextStyle(fontSize: 12),
                      ),
                      const Text(
                        '• أقصى عمولة: 5 جنيه فقط',
                        style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.green),
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          return Text(
                            '• مشاهدة الإعلان: $remainingViews مرة متبقية',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: remainingViews <= 0 ? Colors.red : Colors.blue,
                            ),
                          );
                        },
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          if (_commission > 0 && !_hasWatchedAd && remainingViews > 0) {
                            return Column(
                              children: [
                        const SizedBox(height: 8),
                        const Divider(),
                        const Text(
                          '🎬 شاهد إعلان قصير وألغِ العمولة نهائياً!',
                          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange),
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: _watchAd,
                            icon: const Icon(Icons.play_circle_fill, color: Colors.white),
                            label: const Text(
                              'شاهد الإعلان (30 ثانية)',
                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      Consumer<SupabaseAuthProvider>(
                        builder: (context, authProvider, child) {
                          final remainingViews = authProvider.currentUser?.adViewsRemaining ?? 0;
                          if (_commission > 0 && !_hasWatchedAd && remainingViews <= 0) {
                            return Column(
                              children: [
                        const SizedBox(height: 8),
                        const Divider(),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.red.shade100,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Row(
                            children: [
                              Icon(Icons.warning, color: Colors.red, size: 20),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'لقد استنفدت عدد مرات مشاهدة الإعلان المسموحة. يجب دفع العمولة.',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                                ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      if (_hasWatchedAd) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: const Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.green, size: 20),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'تم إلغاء العمولة! يمكنك التوريد بدون عمولة',
                                  style: TextStyle(
                                    color: Colors.green,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // 📸 حقل رفع صورة إثبات التحويل
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.camera_alt, color: AppTheme.primaryColor),
                          SizedBox(width: 8),
                          Text(
                            'صورة إثبات التحويل',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'يرجى رفع صورة واضحة لإثبات التحويل',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 12),
                      if (_proofImage != null) ...[
                        // عرض الصورة المختارة
                        Container(
                          height: 200,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              _proofImage!,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: _pickImage,
                                icon: const Icon(Icons.edit),
                                label: const Text('تغيير الصورة'),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () {
                                  setState(() {
                                    _proofImage = null;
                                  });
                                  _updateFormState(); // تحديث حالة النموذج
                                },
                                icon: const Icon(Icons.delete, color: Colors.red),
                                label: const Text('حذف الصورة', style: TextStyle(color: Colors.red)),
                              ),
                            ),
                          ],
                        ),
                      ] else ...[
                        // زر رفع الصورة
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: _isUploadingImage ? null : _pickImage,
                            icon: _isUploadingImage
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : const Icon(Icons.add_a_photo),
                            label: Text(_isUploadingImage
                              ? 'جاري رفع الصورة...'
                              : 'إضافة صورة'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(
                                color: _proofImage == null ? Colors.red : AppTheme.primaryColor,
                                width: _proofImage == null ? 2 : 1,
                              ),
                              foregroundColor: _proofImage == null ? Colors.red : AppTheme.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبلغ:'),
                          Text('${_amount.toStringAsFixed(2)} جنيه', style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('العمولة (0.5%):'),
                          Text('${_commission.toStringAsFixed(2)} جنيه', style: const TextStyle(fontWeight: FontWeight.bold)),
                        ],
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('الإجمالي:', style: TextStyle(fontWeight: FontWeight.bold)),
                          Text('${_totalAmount.toStringAsFixed(2)} جنيه',
                            style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryColor, fontSize: 16),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
                ElevatedButton.icon(
                  onPressed: _isFormValid && !transactionProvider.isLoading && !_isSubmitting ? _handleSubmitTransaction : null,
                  icon: (transactionProvider.isLoading || _isSubmitting)
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.check_circle, size: 20),
                  label: Text(
                    (transactionProvider.isLoading || _isSubmitting) ? 'جاري المعالجة...' : 'تم التحويل',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    minimumSize: const Size(double.infinity, 50),
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: const Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.warning_amber, color: Colors.orange, size: 16),
                          SizedBox(width: 8),
                          Text(
                            'تنبيه مهم:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        'تأكد من تحويل المبلغ الإجمالي (شامل العمولة) قبل الضغط على "تم التحويل"',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

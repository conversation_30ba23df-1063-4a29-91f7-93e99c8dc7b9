name: wardly_app
description: تطبيق وردلي لتسهيل عملية توريد الأموال من مندوبي شركة طلبات

publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=2.19.0 <3.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  provider: ^6.0.5
  shared_preferences: ^2.1.0
  intl: ^0.20.2
  url_launcher: ^6.1.10
  supabase_flutter: ^2.5.6
  flutter_svg: ^2.0.5
  http: ^1.1.0
  mailer: ^6.0.1


dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  assets:
    - assets/images/
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300

# إعدادات أيقونة التطبيق (Android والويب فقط)
flutter_launcher_icons:
  android: "launcher_icon"
  image_path: "assets/images/icon0.jpg"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/icon0.jpg"
    background_color: "#FF6B35"
    theme_color: "#FF6B35"